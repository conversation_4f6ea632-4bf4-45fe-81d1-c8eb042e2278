# Phoenix Umbrella 项目结构

本文档定义完整的 Phoenix umbrella 应用架构，包含各子应用的职责、依赖关系和配置文件。

## 整体目录结构

```
tt_quant/
├── mix.exs                    # Umbrella 根配置
├── config/                    # 全局配置
│   ├── config.exs
│   ├── dev.exs
│   ├── prod.exs
│   └── test.exs
├── apps/                      # 子应用集合
│   ├── tt_web/               # Phoenix Web 应用
│   ├── tt_control/           # 控制与编排
│   ├── tt_stream/            # 事件流处理
│   ├── tt_data/              # 数据管理
│   └── tt_native/            # Rust NIF 接口
├── rust/                     # Rust 工作区
│   ├── Cargo.toml
│   └── crates/
└── docs/                     # 文档
```

## 根配置文件

### mix.exs (根目录)

```elixir
defmodule TTQuant.MixProject do
  use Mix.Project

  def project do
    [
      apps_path: "apps",
      version: "0.1.0",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      aliases: aliases(),
      releases: releases()
    ]
  end

  defp deps do
    [
      # 开发工具
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:dialyxir, "~> 1.4", only: [:dev, :test], runtime: false},
      {:ex_doc, "~> 0.31", only: :dev, runtime: false},
      
      # 测试工具
      {:excoveralls, "~> 0.18", only: :test},
      {:benchee, "~> 1.3", only: :dev}
    ]
  end

  defp aliases do
    [
      setup: ["deps.get", "ecto.setup"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.deploy": [
        "cmd --cd apps/tt_web/assets npm run deploy",
        "esbuild default --minify",
        "phx.digest"
      ]
    ]
  end

  defp releases do
    [
      tt_quant: [
        applications: [
          tt_data: :permanent,
          tt_native: :permanent,
          tt_stream: :permanent,
          tt_control: :permanent,
          tt_web: :permanent
        ]
      ]
    ]
  end
end
```

### config/config.exs

```elixir
import Config

# 全局配置
config :tt_quant,
  ecto_repos: [TTData.Repo]

# 日志配置
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id, :user_id, :strategy_id]

# Phoenix 配置
config :phoenix, :json_library, Jason

# 导入环境特定配置
import_config "#{config_env()}.exs"
```

## 子应用详细结构

### 1. tt_web - Phoenix Web 应用

```
apps/tt_web/
├── mix.exs
├── lib/
│   ├── tt_web/
│   │   ├── application.ex
│   │   ├── endpoint.ex
│   │   ├── router.ex
│   │   ├── telemetry.ex
│   │   ├── controllers/
│   │   │   ├── api/
│   │   │   │   ├── strategy_controller.ex
│   │   │   │   ├── account_controller.ex
│   │   │   │   └── market_data_controller.ex
│   │   │   └── page_controller.ex
│   │   ├── live/
│   │   │   ├── dashboard_live.ex
│   │   │   ├── strategy_live.ex
│   │   │   ├── portfolio_live.ex
│   │   │   └── market_live.ex
│   │   ├── components/
│   │   │   ├── core_components.ex
│   │   │   ├── chart_components.ex
│   │   │   └── trading_components.ex
│   │   └── channels/
│   │       ├── user_socket.ex
│   │       └── market_channel.ex
│   └── tt_web.ex
├── assets/
├── priv/
└── test/
```

#### apps/tt_web/mix.exs

```elixir
defmodule TTWeb.MixProject do
  use Mix.Project

  def project do
    [
      app: :tt_web,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.14",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  def application do
    [
      mod: {TTWeb.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  defp deps do
    [
      # Phoenix 核心
      {:phoenix, "~> 1.7.10"},
      {:phoenix_html, "~> 4.0"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 0.20.2"},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      
      # 资产处理
      {:esbuild, "~> 0.8", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.2", runtime: Mix.env() == :dev},
      
      # JSON 处理
      {:jason, "~> 1.2"},
      
      # 内部依赖
      {:tt_control, in_umbrella: true},
      {:tt_stream, in_umbrella: true},
      {:tt_data, in_umbrella: true},
      
      # 工具库
      {:gettext, "~> 0.20"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:plug_cowboy, "~> 2.5"}
    ]
  end
end
```

### 2. tt_control - 控制与编排

```
apps/tt_control/
├── mix.exs
├── lib/
│   ├── tt_control/
│   │   ├── application.ex
│   │   ├── supervisor.ex
│   │   ├── strategy/
│   │   │   ├── manager.ex
│   │   │   ├── supervisor.ex
│   │   │   └── runner.ex
│   │   ├── account/
│   │   │   ├── manager.ex
│   │   │   └── supervisor.ex
│   │   ├── risk/
│   │   │   ├── manager.ex
│   │   │   └── monitor.ex
│   │   └── cluster/
│   │       ├── coordinator.ex
│   │       └── node_manager.ex
│   └── tt_control.ex
└── test/
```

#### apps/tt_control/mix.exs

```elixir
defmodule TTControl.MixProject do
  use Mix.Project

  def project do
    [
      app: :tt_control,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps()
    ]
  end

  def application do
    [
      mod: {TTControl.Application, []},
      extra_applications: [:logger]
    ]
  end

  defp deps do
    [
      # OTP 工具
      {:libcluster, "~> 3.3"},
      {:swarm, "~> 3.4"},
      
      # 任务调度
      {:oban, "~> 2.17"},
      
      # 内部依赖
      {:tt_data, in_umbrella: true},
      {:tt_native, in_umbrella: true},
      {:tt_stream, in_umbrella: true}
    ]
  end
end
```

### 3. tt_stream - 事件流处理

```
apps/tt_stream/
├── mix.exs
├── lib/
│   ├── tt_stream/
│   │   ├── application.ex
│   │   ├── broadway/
│   │   │   ├── market_data_processor.ex
│   │   │   ├── order_processor.ex
│   │   │   └── trade_processor.ex
│   │   ├── pubsub/
│   │   │   ├── market_events.ex
│   │   │   ├── trading_events.ex
│   │   │   └── system_events.ex
│   │   └── connectors/
│   │       ├── websocket_client.ex
│   │       ├── rest_client.ex
│   │       └── exchange_adapter.ex
│   └── tt_stream.ex
└── test/
```

### 4. tt_data - 数据管理

```
apps/tt_data/
├── mix.exs
├── lib/
│   ├── tt_data/
│   │   ├── application.ex
│   │   ├── repo.ex
│   │   ├── schemas/
│   │   │   ├── account.ex
│   │   │   ├── strategy.ex
│   │   │   ├── order.ex
│   │   │   └── trade.ex
│   │   ├── queries/
│   │   │   ├── account_queries.ex
│   │   │   └── trading_queries.ex
│   │   └── catalog/
│   │       ├── parquet_manager.ex
│   │       └── data_loader.ex
│   └── tt_data.ex
├── priv/
│   └── repo/
│       ├── migrations/
│       └── seeds.exs
└── test/
```

### 5. tt_native - Rust NIF 接口

```
apps/tt_native/
├── mix.exs
├── lib/
│   ├── tt_native/
│   │   ├── application.ex
│   │   ├── order_book.ex
│   │   ├── market_data.ex
│   │   ├── execution.ex
│   │   ├── backtest.ex
│   │   └── data_catalog.ex
│   └── tt_native.ex
├── native/
│   └── tt_native/
│       ├── Cargo.toml
│       └── src/
│           └── lib.rs
└── test/
```

#### apps/tt_native/mix.exs

```elixir
defmodule TTNative.MixProject do
  use Mix.Project

  def project do
    [
      app: :tt_native,
      version: "0.1.0",
      build_path: "../../_build",
      config_path: "../../config/config.exs",
      deps_path: "../../deps",
      lockfile: "../../mix.lock",
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      compilers: [:rustler] ++ Mix.compilers(),
      rustler_crates: rustler_crates()
    ]
  end

  def application do
    [
      extra_applications: [:logger]
    ]
  end

  defp deps do
    [
      {:rustler, "~> 0.30"}
    ]
  end

  defp rustler_crates do
    [
      tt_order_book: [
        path: "../../rust/crates/order_book",
        mode: rustler_mode()
      ],
      tt_market_data: [
        path: "../../rust/crates/market_data", 
        mode: rustler_mode()
      ],
      tt_execution: [
        path: "../../rust/crates/execution",
        mode: rustler_mode()
      ]
    ]
  end

  defp rustler_mode do
    case Mix.env() do
      :prod -> :release
      _ -> :debug
    end
  end
end
```

## Rust 工作区结构

### rust/Cargo.toml

```toml
[workspace]
resolver = "2"
members = [
    "crates/order_book",
    "crates/market_data", 
    "crates/execution",
    "crates/risk",
    "crates/backtest",
    "crates/datacatalog",
    "crates/common"
]

[workspace.dependencies]
rustler = "0.30"
serde = { version = "1.0", features = ["derive"] }
bincode = "1.3"
thiserror = "1.0"
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
```

## 启动脚本

### scripts/setup.sh

```bash
#!/bin/bash
set -e

echo "Setting up TT Quant development environment..."

# 安装 Elixir 依赖
mix deps.get

# 设置数据库
mix ecto.setup

# 编译 Rust crates
cd rust && cargo build && cd ..

# 安装前端依赖
cd apps/tt_web/assets && npm install && cd ../../..

# 运行测试
mix test

echo "Setup complete! Run 'iex -S mix phx.server' to start the application."
```

这个 umbrella 结构提供了清晰的关注点分离，每个应用都有明确的职责边界，便于独立开发、测试和部署。
