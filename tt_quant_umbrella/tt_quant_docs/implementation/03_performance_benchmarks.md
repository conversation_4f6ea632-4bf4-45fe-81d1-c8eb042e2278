# 性能基准测试框架

本文档定义 Phoenix + Rust（Rustler）重构版本的性能测试方案，包含延迟、吞吐量基准测试，与 NautilusTrader 原版对比，以及硬件环境要求。

## 测试目标与指标

### 核心性能指标

1. **延迟指标**
   - 单次 NIF 调用延迟（P50/P95/P99/P99.9）
   - 端到端处理延迟：行情接收 → 盘口更新 → 信号生成 → 订单发送
   - 跨语言边界开销：Elixir ↔ Rust 数据传输时间

2. **吞吐量指标**
   - 订单簿更新速率（deltas/second）
   - 并发策略处理能力（strategies/core）
   - 市场数据处理速率（ticks/second）

3. **资源使用指标**
   - CPU 使用率（用户态/内核态）
   - 内存使用量（RSS/堆内存/NIF 资源）
   - GC 压力（BEAM GC 频率与停顿时间）

4. **稳定性指标**
   - 长时间运行稳定性（24小时+）
   - 内存泄漏检测
   - 错误恢复时间

## 基准测试套件

### 1. 微基准测试（Micro Benchmarks）

```elixir
# lib/tt_quant/benchmarks/micro_benchmarks.ex
defmodule TTQuant.Benchmarks.MicroBenchmarks do
  @moduledoc "微基准测试：单个 NIF 函数性能"
  
  def run_all do
    [
      &benchmark_orderbook_operations/0,
      &benchmark_market_data_parsing/0,
      &benchmark_risk_checks/0,
      &benchmark_serialization/0
    ]
    |> Enum.each(& &1.())
  end

  def benchmark_orderbook_operations do
    # 准备测试数据
    book_bin = TTNative.OrderBook.new("BTCUSD", "binance")
    
    single_delta = create_test_delta(1)
    batch_deltas_10 = create_test_deltas(10)
    batch_deltas_100 = create_test_deltas(100)
    batch_deltas_1000 = create_test_deltas(1000)

    Benchee.run(
      %{
        "apply_single_delta" => fn -> 
          TTNative.OrderBook.apply_deltas(book_bin, [single_delta])
        end,
        "apply_10_deltas" => fn -> 
          TTNative.OrderBook.apply_deltas(book_bin, batch_deltas_10)
        end,
        "apply_100_deltas" => fn -> 
          TTNative.OrderBook.apply_deltas(book_bin, batch_deltas_100)
        end,
        "apply_1000_deltas" => fn -> 
          TTNative.OrderBook.apply_deltas(book_bin, batch_deltas_1000)
        end,
        "best_bid_ask" => fn -> 
          TTNative.OrderBook.best_bid_ask(book_bin)
        end,
        "get_depth_10" => fn -> 
          TTNative.OrderBook.get_depth(book_bin, 10)
        end
      },
      time: 10,
      memory_time: 2,
      formatters: [
        Benchee.Formatters.HTML,
        Benchee.Formatters.Console
      ],
      html: [file: "benchmarks/results/orderbook_micro.html"]
    )
  end

  def benchmark_market_data_parsing do
    # 测试不同格式的市场数据解析性能
    json_data = create_test_json_data(1000)
    cbor_data = create_test_cbor_data(1000)
    protobuf_data = create_test_protobuf_data(1000)

    Benchee.run(
      %{
        "parse_json_1000_ticks" => fn -> 
          TTNative.MarketData.decode_batch(json_data, "json")
        end,
        "parse_cbor_1000_ticks" => fn -> 
          TTNative.MarketData.decode_batch(cbor_data, "cbor")
        end,
        "parse_protobuf_1000_ticks" => fn -> 
          TTNative.MarketData.decode_batch(protobuf_data, "protobuf")
        end
      },
      time: 10,
      html: [file: "benchmarks/results/market_data_micro.html"]
    )
  end

  # 辅助函数
  defp create_test_delta(sequence) do
    %{
      side: :bid,
      action: :add,
      price: {50000_00000000 + sequence * 100000, 8},
      quantity: {1_000000, 6},
      timestamp: System.system_time(:nanosecond),
      sequence: sequence
    }
  end

  defp create_test_deltas(count) do
    1..count |> Enum.map(&create_test_delta/1)
  end
end
```

### 2. 端到端基准测试

```elixir
# lib/tt_quant/benchmarks/e2e_benchmarks.ex
defmodule TTQuant.Benchmarks.E2EBenchmarks do
  @moduledoc "端到端基准测试：完整交易流程性能"
  
  def run_trading_pipeline_benchmark do
    # 模拟完整的交易管道：行情 → 策略 → 风控 → 执行
    market_data_stream = create_market_data_stream(10_000)
    strategy_config = create_test_strategy_config()
    risk_params = create_test_risk_params()

    Benchee.run(
      %{
        "full_trading_pipeline" => fn ->
          market_data_stream
          |> process_market_data()
          |> run_strategy(strategy_config)
          |> apply_risk_checks(risk_params)
          |> generate_orders()
        end
      },
      time: 30,
      memory_time: 5,
      html: [file: "benchmarks/results/e2e_trading.html"]
    )
  end

  def run_concurrent_strategies_benchmark do
    # 测试多策略并发处理能力
    strategies = 1..100 |> Enum.map(&create_test_strategy/1)
    market_data = create_market_data_stream(1000)

    Benchee.run(
      %{
        "1_strategy" => fn -> run_strategies([Enum.at(strategies, 0)], market_data) end,
        "10_strategies" => fn -> run_strategies(Enum.take(strategies, 10), market_data) end,
        "50_strategies" => fn -> run_strategies(Enum.take(strategies, 50), market_data) end,
        "100_strategies" => fn -> run_strategies(strategies, market_data) end
      },
      time: 20,
      html: [file: "benchmarks/results/concurrent_strategies.html"]
    )
  end

  # 延迟分布测试
  def run_latency_distribution_test do
    book_bin = TTNative.OrderBook.new("BTCUSD", "test")
    deltas = create_test_deltas(1000)

    # 收集大量样本以分析延迟分布
    latencies = for _i <- 1..10_000 do
      {time, _result} = :timer.tc(fn -> 
        TTNative.OrderBook.apply_deltas(book_bin, [Enum.random(deltas)])
      end)
      time # 微秒
    end

    # 计算统计指标
    sorted_latencies = Enum.sort(latencies)
    count = length(sorted_latencies)
    
    stats = %{
      min: Enum.min(sorted_latencies),
      max: Enum.max(sorted_latencies),
      mean: Enum.sum(sorted_latencies) / count,
      p50: Enum.at(sorted_latencies, trunc(count * 0.5)),
      p95: Enum.at(sorted_latencies, trunc(count * 0.95)),
      p99: Enum.at(sorted_latencies, trunc(count * 0.99)),
      p99_9: Enum.at(sorted_latencies, trunc(count * 0.999))
    }

    IO.puts("Latency Distribution (microseconds):")
    IO.inspect(stats, pretty: true)
    
    # 保存详细数据用于进一步分析
    File.write!("benchmarks/results/latency_distribution.json", Jason.encode!(stats))
  end
end
```

### 3. 压力测试

```elixir
# lib/tt_quant/benchmarks/stress_tests.ex
defmodule TTQuant.Benchmarks.StressTests do
  @moduledoc "压力测试：极限负载下的系统表现"
  
  def run_high_frequency_updates do
    # 模拟高频交易场景：每秒数万次订单簿更新
    book_bin = TTNative.OrderBook.new("BTCUSD", "test")
    
    # 启动多个并发进程，每个进程持续发送更新
    num_processes = System.schedulers_online() * 2
    updates_per_process = 10_000
    
    start_time = System.monotonic_time(:millisecond)
    
    tasks = for i <- 1..num_processes do
      Task.async(fn ->
        deltas = create_random_deltas(updates_per_process, i * 1000)
        
        Enum.each(deltas, fn delta ->
          TTNative.OrderBook.apply_deltas(book_bin, [delta])
        end)
      end)
    end
    
    Task.await_many(tasks, 60_000) # 60秒超时
    
    end_time = System.monotonic_time(:millisecond)
    total_time = end_time - start_time
    total_updates = num_processes * updates_per_process
    
    IO.puts("Stress Test Results:")
    IO.puts("  Total updates: #{total_updates}")
    IO.puts("  Total time: #{total_time}ms")
    IO.puts("  Updates/second: #{trunc(total_updates * 1000 / total_time)}")
  end

  def run_memory_pressure_test do
    # 内存压力测试：创建大量订单簿并持续更新
    initial_memory = :erlang.memory(:total)
    
    books = for i <- 1..1000 do
      book = TTNative.OrderBook.new("SYMBOL#{i}", "test")
      # 为每个订单簿添加一些初始数据
      deltas = create_test_deltas(100)
      {:ok, updated_book} = TTNative.OrderBook.apply_deltas(book, deltas)
      updated_book
    end
    
    peak_memory = :erlang.memory(:total)
    
    # 清理并检查内存回收
    books = nil
    :erlang.garbage_collect()
    :timer.sleep(1000) # 等待GC完成
    
    final_memory = :erlang.memory(:total)
    
    IO.puts("Memory Pressure Test Results:")
    IO.puts("  Initial memory: #{initial_memory} bytes")
    IO.puts("  Peak memory: #{peak_memory} bytes")
    IO.puts("  Final memory: #{final_memory} bytes")
    IO.puts("  Memory increase: #{peak_memory - initial_memory} bytes")
    IO.puts("  Memory recovered: #{peak_memory - final_memory} bytes")
  end
end
```

## 与 NautilusTrader 对比测试

### 对比测试框架

```elixir
# lib/tt_quant/benchmarks/nautilus_comparison.ex
defmodule TTQuant.Benchmarks.NautilusComparison do
  @moduledoc "与 NautilusTrader 原版性能对比"
  
  def run_comparison_suite do
    # 注意：需要安装 NautilusTrader Python 包
    # 通过 Port 调用 Python 脚本进行对比测试
    
    test_cases = [
      {"orderbook_updates", &compare_orderbook_performance/0},
      {"market_data_parsing", &compare_parsing_performance/0},
      {"strategy_execution", &compare_strategy_performance/0}
    ]
    
    results = Enum.map(test_cases, fn {name, test_fn} ->
      IO.puts("Running comparison test: #{name}")
      {name, test_fn.()}
    end)
    
    # 生成对比报告
    generate_comparison_report(results)
  end

  defp compare_orderbook_performance do
    # Phoenix + Rust 版本
    phoenix_time = benchmark_phoenix_orderbook()
    
    # NautilusTrader 原版（通过 Python 脚本）
    nautilus_time = benchmark_nautilus_orderbook()
    
    %{
      phoenix_rust: phoenix_time,
      nautilus_original: nautilus_time,
      speedup: nautilus_time / phoenix_time
    }
  end

  defp benchmark_phoenix_orderbook do
    book_bin = TTNative.OrderBook.new("BTCUSD", "test")
    deltas = create_test_deltas(10_000)
    
    {time, _result} = :timer.tc(fn ->
      TTNative.OrderBook.apply_deltas(book_bin, deltas)
    end)
    
    time / 1_000_000 # 转换为秒
  end

  defp benchmark_nautilus_orderbook do
    # 调用 Python 脚本进行 NautilusTrader 基准测试
    python_script = """
    import time
    from nautilus_trader.model.data import OrderBookDelta
    from nautilus_trader.model.orderbook import OrderBook
    
    # 创建测试数据
    book = OrderBook.create(instrument_id="BTCUSD.BINANCE")
    deltas = create_test_deltas(10000)  # 需要实现这个函数
    
    start_time = time.time()
    for delta in deltas:
        book.apply_delta(delta)
    end_time = time.time()
    
    print(end_time - start_time)
    """
    
    # 执行 Python 脚本并获取结果
    {result, 0} = System.cmd("python3", ["-c", python_script])
    String.to_float(String.trim(result))
  end
end
```

## 硬件环境要求

### 推荐测试环境

```yaml
# benchmarks/config/hardware_requirements.yml
minimum_requirements:
  cpu:
    cores: 4
    frequency: "2.4 GHz"
    architecture: "x86_64"
  memory:
    ram: "8 GB"
    type: "DDR4"
  storage:
    type: "SSD"
    space: "50 GB"
  network:
    bandwidth: "1 Gbps"

recommended_requirements:
  cpu:
    cores: 16
    frequency: "3.2 GHz"
    architecture: "x86_64"
    features: ["AVX2", "TSX"]
  memory:
    ram: "32 GB"
    type: "DDR4-3200"
  storage:
    type: "NVMe SSD"
    space: "200 GB"
    iops: "100k+"
  network:
    bandwidth: "10 Gbps"
    latency: "< 1ms"

optimal_requirements:
  cpu:
    cores: 32
    frequency: "4.0 GHz"
    architecture: "x86_64"
    features: ["AVX-512", "TSX"]
  memory:
    ram: "128 GB"
    type: "DDR5-4800"
  storage:
    type: "Optane SSD"
    space: "1 TB"
    iops: "500k+"
  network:
    bandwidth: "25 Gbps"
    latency: "< 0.1ms"
```

### 环境配置脚本

```bash
#!/bin/bash
# benchmarks/scripts/setup_benchmark_env.sh

set -e

echo "Setting up benchmark environment..."

# 系统优化
echo "Applying system optimizations..."
sudo sysctl -w kernel.sched_migration_cost_ns=5000000
sudo sysctl -w kernel.sched_autogroup_enabled=0
sudo sysctl -w vm.swappiness=1

# CPU 频率固定
echo "Setting CPU governor to performance..."
sudo cpupower frequency-set -g performance

# 禁用不必要的服务
echo "Disabling unnecessary services..."
sudo systemctl stop bluetooth
sudo systemctl stop cups

# 设置进程优先级
echo "Setting process priorities..."
sudo renice -20 -p $$

# 验证环境
echo "Verifying environment..."
echo "CPU info:"
lscpu | grep -E "(Model name|CPU\(s\)|Thread|Core|Socket)"

echo "Memory info:"
free -h

echo "Storage info:"
df -h

echo "Network info:"
ip link show

echo "Benchmark environment setup complete!"
```

## 自动化测试流水线

```elixir
# lib/tt_quant/benchmarks/automation.ex
defmodule TTQuant.Benchmarks.Automation do
  @moduledoc "自动化基准测试流水线"
  
  def run_full_benchmark_suite do
    results = %{}
    
    # 1. 环境检查
    IO.puts("Checking environment...")
    env_check = check_environment()
    results = Map.put(results, :environment, env_check)
    
    # 2. 微基准测试
    IO.puts("Running micro benchmarks...")
    micro_results = TTQuant.Benchmarks.MicroBenchmarks.run_all()
    results = Map.put(results, :micro_benchmarks, micro_results)
    
    # 3. 端到端测试
    IO.puts("Running end-to-end benchmarks...")
    e2e_results = TTQuant.Benchmarks.E2EBenchmarks.run_all()
    results = Map.put(results, :e2e_benchmarks, e2e_results)
    
    # 4. 压力测试
    IO.puts("Running stress tests...")
    stress_results = TTQuant.Benchmarks.StressTests.run_all()
    results = Map.put(results, :stress_tests, stress_results)
    
    # 5. 对比测试
    IO.puts("Running comparison tests...")
    comparison_results = TTQuant.Benchmarks.NautilusComparison.run_comparison_suite()
    results = Map.put(results, :comparisons, comparison_results)
    
    # 6. 生成报告
    generate_comprehensive_report(results)
    
    results
  end

  defp check_environment do
    %{
      elixir_version: System.version(),
      erlang_version: System.otp_release(),
      cpu_count: System.schedulers_online(),
      memory_total: :erlang.memory(:total),
      system_architecture: :erlang.system_info(:system_architecture)
    }
  end

  defp generate_comprehensive_report(results) do
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601()
    filename = "benchmarks/reports/comprehensive_#{timestamp}.json"
    
    File.write!(filename, Jason.encode!(results, pretty: true))
    IO.puts("Comprehensive benchmark report saved to: #{filename}")
  end
end
```

这个性能基准测试框架提供了全面的性能评估能力，可以准确测量 Phoenix + Rust 重构版本的性能表现，并与原版 NautilusTrader 进行对比，为优化决策提供数据支持。
