# Phoenix 开发工作流程

本文档定义 Phoenix 版本开发的具体工作流程、开发规范和最佳实践。

## 开发环境搭建

### 必需工具
```bash
# Elixir 和 Erlang
asdf install erlang 26.2.1
asdf install elixir 1.16.0-otp-26
asdf global erlang 26.2.1
asdf global elixir 1.16.0-otp-26

# Rust 工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup default stable
rustup component add clippy rustfmt

# Node.js（用于前端资源）
asdf install nodejs 20.10.0
asdf global nodejs 20.10.0

# 数据库
brew install postgresql@15
brew install redis
```

### 项目初始化
```bash
# 创建 umbrella 项目
mix new tt_quant --umbrella
cd tt_quant

# 创建子应用
cd apps
mix phx.new tt_web --no-ecto
mix new tt_control --sup
mix new tt_stream --sup  
mix new tt_data --sup
mix new tt_native --sup

# 配置 Rust 工作区
mkdir -p rust/crates
cd rust && cargo init --name tt_quant_workspace
```

## Git 工作流程

### 分支策略
```
main                    # 生产分支
├── develop            # 开发主分支
├── feature/xxx        # 功能分支
├── hotfix/xxx         # 热修复分支
└── release/vx.x.x     # 发布分支
```

### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复 bug
docs:     文档更新
style:    代码格式调整
refactor: 重构
test:     测试相关
chore:    构建/工具相关

# 示例
feat(order_book): implement delta updates
fix(market_data): handle websocket reconnection
docs(api): update strategy configuration guide
```

### PR 流程
1. **创建功能分支**：从 develop 分支创建
2. **开发实现**：遵循编码规范
3. **自测验证**：运行测试和 lint
4. **提交 PR**：填写详细描述
5. **代码审查**：至少 2 人审查
6. **合并主分支**：squash merge

## 编码规范

### Elixir 代码规范
```elixir
# 模块文档
defmodule TTControl.Strategy.Manager do
  @moduledoc """
  策略管理器，负责策略的生命周期管理。
  
  ## 功能
  - 策略启动和停止
  - 状态监控
  - 错误恢复
  """
  
  use DynamicSupervisor
  
  @doc """
  启动策略实例
  
  ## 参数
  - strategy_config: 策略配置
  
  ## 返回
  - {:ok, pid} | {:error, reason}
  """
  @spec start_strategy(map()) :: {:ok, pid()} | {:error, term()}
  def start_strategy(strategy_config) do
    # 实现逻辑
  end
end
```

### Rust 代码规范
```rust
//! 订单簿核心模块
//! 
//! 提供高性能的订单簿数据结构和操作接口

use rustler::{Binary, NifResult, OwnedBinary};
use serde::{Deserialize, Serialize};

/// 价格类型，使用定点数表示
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[repr(C)]
pub struct Price {
    /// 定点数值
    pub value: i64,
    /// 小数位数
    pub precision: u8,
    /// 填充字节，确保内存对齐
    pub _padding: [u8; 7],
}

impl Price {
    /// 创建新的价格实例
    /// 
    /// # 参数
    /// - value: 定点数值
    /// - precision: 小数位数
    /// 
    /// # 示例
    /// ```rust
    /// let price = Price::new(100_000_000, 8); // 1.0 with 8 decimal places
    /// ```
    pub fn new(value: i64, precision: u8) -> Self {
        Self {
            value,
            precision,
            _padding: [0; 7],
        }
    }
}
```

## 测试策略

### 单元测试
```elixir
# test/tt_control/strategy/manager_test.exs
defmodule TTControl.Strategy.ManagerTest do
  use ExUnit.Case, async: true
  
  alias TTControl.Strategy.Manager
  
  describe "start_strategy/1" do
    test "starts strategy with valid config" do
      config = %{
        strategy_id: "test_strategy",
        module: TestStrategy,
        params: %{}
      }
      
      assert {:ok, pid} = Manager.start_strategy(config)
      assert Process.alive?(pid)
    end
    
    test "returns error with invalid config" do
      config = %{}
      assert {:error, _reason} = Manager.start_strategy(config)
    end
  end
end
```

### 集成测试
```elixir
# test/integration/market_data_flow_test.exs
defmodule Integration.MarketDataFlowTest do
  use ExUnit.Case
  
  test "end-to-end market data processing" do
    # 1. 启动市场数据连接器
    {:ok, connector} = TTStream.Connectors.WebSocketClient.start_link(test_config())
    
    # 2. 订阅市场数据事件
    Phoenix.PubSub.subscribe(TTStream.PubSub, "market_data")
    
    # 3. 发送测试数据
    send(connector, {:websocket, test_market_data()})
    
    # 4. 验证处理结果
    assert_receive {:market_data, processed_data}, 1000
    assert processed_data.symbol == "BTCUSD"
  end
end
```

### 性能测试
```elixir
# benchmarks/order_book_bench.exs
defmodule OrderBookBench do
  use Benchee
  
  def run do
    book_bin = TTNative.OrderBook.new("BTCUSD", "test")
    deltas = create_test_deltas(1000)
    
    Benchee.run(
      %{
        "apply_1000_deltas" => fn -> 
          TTNative.OrderBook.apply_deltas(book_bin, deltas)
        end,
        "best_bid_ask" => fn ->
          TTNative.OrderBook.best_bid_ask(book_bin)
        end
      },
      time: 10,
      memory_time: 2,
      formatters: [
        Benchee.Formatters.HTML,
        Benchee.Formatters.Console
      ]
    )
  end
end
```

## CI/CD 配置

### GitHub Actions
```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Elixir
      uses: erlef/setup-beam@v1
      with:
        elixir-version: '1.16.0'
        otp-version: '26.2.1'
    
    - name: Setup Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          deps
          _build
          rust/target
        key: ${{ runner.os }}-mix-${{ hashFiles('**/mix.lock') }}
    
    - name: Install dependencies
      run: mix deps.get
    
    - name: Check formatting
      run: |
        mix format --check-formatted
        cd rust && cargo fmt --check
    
    - name: Run linter
      run: |
        mix credo --strict
        cd rust && cargo clippy -- -D warnings
    
    - name: Run tests
      run: |
        mix test --cover
        cd rust && cargo test
    
    - name: Run benchmarks
      run: mix run benchmarks/order_book_bench.exs
```

## 部署流程

### 容器化
```dockerfile
# Dockerfile
FROM hexpm/elixir:1.16.0-erlang-26.2.1-alpine-3.18.4 AS builder

# 安装构建依赖
RUN apk add --no-cache build-base git curl

# 安装 Rust
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH="/root/.cargo/bin:${PATH}"

WORKDIR /app

# 复制依赖文件
COPY mix.exs mix.lock ./
COPY apps/*/mix.exs apps/*/
COPY rust/ rust/

# 安装依赖
RUN mix local.hex --force && \
    mix local.rebar --force && \
    mix deps.get --only prod

# 复制源代码
COPY . .

# 编译应用
RUN mix compile
RUN mix assets.deploy
RUN mix release

# 运行时镜像
FROM alpine:3.18.4

RUN apk add --no-cache openssl ncurses-libs

WORKDIR /app

COPY --from=builder /app/_build/prod/rel/tt_quant ./

EXPOSE 4000

CMD ["./bin/tt_quant", "start"]
```

### Kubernetes 部署
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tt-quant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tt-quant
  template:
    metadata:
      labels:
        app: tt-quant
    spec:
      containers:
      - name: tt-quant
        image: tt-quant:latest
        ports:
        - containerPort: 4000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: tt-quant-secrets
              key: database-url
        - name: SECRET_KEY_BASE
          valueFrom:
            secretKeyRef:
              name: tt-quant-secrets
              key: secret-key-base
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 4000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 监控和日志

### 监控配置
```elixir
# lib/tt_web/telemetry.ex
defmodule TTWeb.Telemetry do
  use Supervisor
  import Telemetry.Metrics
  
  def start_link(arg) do
    Supervisor.start_link(__MODULE__, arg, name: __MODULE__)
  end
  
  def init(_arg) do
    children = [
      {:telemetry_poller, measurements: periodic_measurements(), period: 10_000},
      {TelemetryMetricsPrometheus, metrics: metrics()}
    ]
    
    Supervisor.init(children, strategy: :one_for_one)
  end
  
  defp metrics do
    [
      # Phoenix 指标
      summary("phoenix.endpoint.stop.duration",
        unit: {:native, :millisecond}
      ),
      
      # 订单簿指标
      counter("tt.order_book.updates.total"),
      summary("tt.order_book.update.duration",
        unit: {:native, :microsecond}
      ),
      
      # 策略指标
      counter("tt.strategy.executions.total"),
      summary("tt.strategy.execution.duration",
        unit: {:native, :millisecond}
      )
    ]
  end
end
```

这个工作流程文档为团队提供了详细的开发指导，确保代码质量和项目进度。
