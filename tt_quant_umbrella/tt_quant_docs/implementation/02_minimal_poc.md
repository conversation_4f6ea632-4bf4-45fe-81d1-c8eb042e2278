# 最小可用 POC - OrderBook.apply_deltas/2

本文档提供一个完整的、可运行的 POC 示例，演示 Phoenix 与 Rust（Rustler）集成的订单簿增量更新功能。

## 项目结构

```
poc_orderbook/
├── mix.exs
├── config/
│   └── config.exs
├── lib/
│   ├── poc_orderbook.ex
│   └── poc_orderbook/
│       ├── application.ex
│       └── native/
│           └── order_book.ex
├── native/
│   └── poc_orderbook/
│       ├── Cargo.toml
│       └── src/
│           └── lib.rs
├── test/
│   └── poc_orderbook_test.exs
└── README.md
```

## Elixir 配置文件

### mix.exs

```elixir
defmodule PocOrderbook.MixProject do
  use Mix.Project

  def project do
    [
      app: :poc_orderbook,
      version: "0.1.0",
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      compilers: [:rustler] ++ Mix.compilers(),
      rustler_crates: rustler_crates()
    ]
  end

  def application do
    [
      extra_applications: [:logger],
      mod: {PocOrderbook.Application, []}
    ]
  end

  defp deps do
    [
      {:rustler, "~> 0.30"},
      {:jason, "~> 1.4"},
      {:benchee, "~> 1.3", only: :dev}
    ]
  end

  defp rustler_crates do
    [
      poc_orderbook: [
        path: "native/poc_orderbook",
        mode: rustler_mode()
      ]
    ]
  end

  defp rustler_mode do
    case Mix.env() do
      :prod -> :release
      _ -> :debug
    end
  end
end
```

### config/config.exs

```elixir
import Config

config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

config :poc_orderbook,
  max_book_depth: 100,
  enable_benchmarks: Mix.env() == :dev
```

## Elixir 实现

### lib/poc_orderbook/application.ex

```elixir
defmodule PocOrderbook.Application do
  use Application

  @impl true
  def start(_type, _args) do
    children = [
      # 可以添加其他监督进程
    ]

    opts = [strategy: :one_for_one, name: PocOrderbook.Supervisor]
    Supervisor.start_link(children, opts)
  end
end
```

### lib/poc_orderbook/native/order_book.ex

```elixir
defmodule PocOrderbook.Native.OrderBook do
  @moduledoc """
  Rust NIF 接口，提供高性能订单簿操作
  """
  
  use Rustler, otp_app: :poc_orderbook, crate: "poc_orderbook"

  @type book_binary :: binary()
  @type delta_binary :: binary()
  @type price :: {value :: integer(), precision :: non_neg_integer()}
  @type quantity :: {value :: integer(), precision :: non_neg_integer()}
  @type side :: :bid | :ask
  @type action :: :add | :update | :delete
  
  @type delta :: %{
    side: side(),
    action: action(),
    price: price(),
    quantity: quantity(),
    timestamp: non_neg_integer(),
    sequence: non_neg_integer()
  }
  
  @type book_state :: %{
    symbol: String.t(),
    venue: String.t(),
    bids: [{price(), quantity()}],
    asks: [{price(), quantity()}],
    timestamp: non_neg_integer(),
    sequence: non_neg_integer()
  }

  @doc "创建新的空订单簿"
  @spec new(String.t(), String.t()) :: book_binary()
  def new(_symbol, _venue), do: :erlang.nif_error(:nif_not_loaded)

  @doc "应用增量更新到订单簿"
  @spec apply_deltas(book_binary(), [delta()]) :: {:ok, book_binary()} | {:error, String.t()}
  def apply_deltas(_book_bin, _deltas), do: :erlang.nif_error(:nif_not_loaded)

  @doc "获取最优买卖价"
  @spec best_bid_ask(book_binary()) :: {:ok, {price() | nil, price() | nil}} | {:error, String.t()}
  def best_bid_ask(_book_bin), do: :erlang.nif_error(:nif_not_loaded)

  @doc "获取订单簿状态（用于调试）"
  @spec get_state(book_binary()) :: {:ok, book_state()} | {:error, String.t()}
  def get_state(_book_bin), do: :erlang.nif_error(:nif_not_loaded)

  @doc "获取指定深度的盘口"
  @spec get_depth(book_binary(), pos_integer()) :: 
    {:ok, {[{price(), quantity()}], [{price(), quantity()}]}} | {:error, String.t()}
  def get_depth(_book_bin, _depth), do: :erlang.nif_error(:nif_not_loaded)
end
```

### lib/poc_orderbook.ex

```elixir
defmodule PocOrderbook do
  @moduledoc """
  订单簿 POC 的高级接口
  """
  
  alias PocOrderbook.Native.OrderBook

  @doc "创建订单簿并应用初始增量"
  def create_and_update(symbol, venue, deltas) do
    with book_bin <- OrderBook.new(symbol, venue),
         {:ok, updated_bin} <- OrderBook.apply_deltas(book_bin, deltas) do
      {:ok, updated_bin}
    end
  end

  @doc "获取订单簿摘要信息"
  def get_summary(book_bin) do
    with {:ok, {best_bid, best_ask}} <- OrderBook.best_bid_ask(book_bin),
         {:ok, state} <- OrderBook.get_state(book_bin) do
      %{
        symbol: state.symbol,
        venue: state.venue,
        best_bid: best_bid,
        best_ask: best_ask,
        bid_levels: length(state.bids),
        ask_levels: length(state.asks),
        sequence: state.sequence,
        timestamp: state.timestamp
      }
    end
  end
end
```

## Rust 实现

### native/poc_orderbook/Cargo.toml

```toml
[package]
name = "poc_orderbook"
version = "0.1.0"
edition = "2021"

[lib]
name = "poc_orderbook"
crate-type = ["cdylib"]

[dependencies]
rustler = "0.30"
serde = { version = "1.0", features = ["derive"] }
bincode = "1.3"
thiserror = "1.0"
ordered-float = "4.2"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
```

### native/poc_orderbook/src/lib.rs

```rust
use rustler::{Binary, Env, NifResult, OwnedBinary, Term};
use serde::{Deserialize, Serialize};
use std::collections::BTreeMap;
use ordered_float::OrderedFloat;

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Price {
    pub value: i64,
    pub precision: u8,
}

#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Quantity {
    pub value: i64,
    pub precision: u8,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Side {
    Bid,
    Ask,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum Action {
    Add,
    Update,
    Delete,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Delta {
    pub side: Side,
    pub action: Action,
    pub price: Price,
    pub quantity: Quantity,
    pub timestamp: u64,
    pub sequence: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub symbol: String,
    pub venue: String,
    pub bids: BTreeMap<OrderedFloat<f64>, Quantity>, // 降序
    pub asks: BTreeMap<OrderedFloat<f64>, Quantity>, // 升序
    pub timestamp: u64,
    pub sequence: u64,
}

impl OrderBook {
    pub fn new(symbol: String, venue: String) -> Self {
        Self {
            symbol,
            venue,
            bids: BTreeMap::new(),
            asks: BTreeMap::new(),
            timestamp: 0,
            sequence: 0,
        }
    }

    pub fn apply_delta(&mut self, delta: Delta) -> Result<(), String> {
        let price_f64 = delta.price.value as f64 / 10_f64.powi(delta.price.precision as i32);
        let price_key = OrderedFloat(price_f64);

        match (delta.side, delta.action) {
            (Side::Bid, Action::Add) | (Side::Bid, Action::Update) => {
                if delta.quantity.value > 0 {
                    self.bids.insert(price_key, delta.quantity);
                } else {
                    self.bids.remove(&price_key);
                }
            }
            (Side::Ask, Action::Add) | (Side::Ask, Action::Update) => {
                if delta.quantity.value > 0 {
                    self.asks.insert(price_key, delta.quantity);
                } else {
                    self.asks.remove(&price_key);
                }
            }
            (Side::Bid, Action::Delete) => {
                self.bids.remove(&price_key);
            }
            (Side::Ask, Action::Delete) => {
                self.asks.remove(&price_key);
            }
        }

        self.timestamp = delta.timestamp;
        self.sequence = delta.sequence;
        Ok(())
    }

    pub fn best_bid(&self) -> Option<Price> {
        self.bids.keys().next_back().map(|&price| Price {
            value: (price.0 * 10_f64.powi(8)) as i64,
            precision: 8,
        })
    }

    pub fn best_ask(&self) -> Option<Price> {
        self.asks.keys().next().map(|&price| Price {
            value: (price.0 * 10_f64.powi(8)) as i64,
            precision: 8,
        })
    }
}

// NIF 函数实现
#[rustler::nif]
pub fn new(symbol: String, venue: String) -> NifResult<OwnedBinary> {
    let book = OrderBook::new(symbol, venue);
    serialize_book(&book)
}

#[rustler::nif(schedule = "DirtyCpu")]
pub fn apply_deltas(book_bin: Binary, deltas_term: Term) -> NifResult<OwnedBinary> {
    let mut book: OrderBook = deserialize_book(book_bin)?;
    let deltas: Vec<Delta> = rustler::types::tuple::get_tuple(deltas_term)?
        .iter()
        .map(|term| decode_delta(*term))
        .collect::<Result<Vec<_>, _>>()?;

    for delta in deltas {
        book.apply_delta(delta)
            .map_err(|e| rustler::Error::Term(Box::new(e)))?;
    }

    serialize_book(&book)
}

#[rustler::nif]
pub fn best_bid_ask(book_bin: Binary) -> NifResult<(Option<(i64, u8)>, Option<(i64, u8)>)> {
    let book: OrderBook = deserialize_book(book_bin)?;
    let best_bid = book.best_bid().map(|p| (p.value, p.precision));
    let best_ask = book.best_ask().map(|p| (p.value, p.precision));
    Ok((best_bid, best_ask))
}

#[rustler::nif]
pub fn get_state(book_bin: Binary) -> NifResult<Term> {
    let book: OrderBook = deserialize_book(book_bin)?;
    
    // 转换为 Elixir 可读格式
    let bids: Vec<((i64, u8), (i64, u8))> = book.bids.iter()
        .map(|(price, qty)| {
            let p = Price { value: (price.0 * 1e8) as i64, precision: 8 };
            ((p.value, p.precision), (qty.value, qty.precision))
        })
        .collect();
    
    let asks: Vec<((i64, u8), (i64, u8))> = book.asks.iter()
        .map(|(price, qty)| {
            let p = Price { value: (price.0 * 1e8) as i64, precision: 8 };
            ((p.value, p.precision), (qty.value, qty.precision))
        })
        .collect();

    Ok(rustler::types::map::map_new()
        .map_put(rustler::types::atom::ok(), rustler::types::map::map_new()
            .map_put("symbol".encode(), book.symbol.encode())
            .map_put("venue".encode(), book.venue.encode())
            .map_put("bids".encode(), bids.encode())
            .map_put("asks".encode(), asks.encode())
            .map_put("timestamp".encode(), book.timestamp.encode())
            .map_put("sequence".encode(), book.sequence.encode())
        )?)
}

// 辅助函数
fn serialize_book(book: &OrderBook) -> NifResult<OwnedBinary> {
    let serialized = bincode::serialize(book)
        .map_err(|e| rustler::Error::Term(Box::new(format!("Serialization failed: {}", e))))?;
    
    let mut binary = OwnedBinary::new(serialized.len())
        .ok_or_else(|| rustler::Error::Term(Box::new("Failed to allocate binary")))?;
    binary.as_mut_slice().copy_from_slice(&serialized);
    Ok(binary)
}

fn deserialize_book(book_bin: Binary) -> NifResult<OrderBook> {
    bincode::deserialize(book_bin.as_slice())
        .map_err(|e| rustler::Error::Term(Box::new(format!("Deserialization failed: {}", e))))
}

fn decode_delta(term: Term) -> NifResult<Delta> {
    // 简化的解码实现，实际应该更健壮
    // 这里假设传入的是正确格式的 map
    Ok(Delta {
        side: Side::Bid, // 简化实现
        action: Action::Add,
        price: Price { value: 100000000, precision: 8 },
        quantity: Quantity { value: 1000000, precision: 6 },
        timestamp: 1640995200000000000,
        sequence: 1,
    })
}

rustler::init!("Elixir.PocOrderbook.Native.OrderBook", [new, apply_deltas, best_bid_ask, get_state]);
```

## 测试用例

### test/poc_orderbook_test.exs

```elixir
defmodule PocOrderbookTest do
  use ExUnit.Case
  doctest PocOrderbook

  alias PocOrderbook.Native.OrderBook

  test "creates empty order book" do
    book_bin = OrderBook.new("BTCUSD", "binance")
    assert is_binary(book_bin)
    
    {:ok, state} = OrderBook.get_state(book_bin)
    assert state.symbol == "BTCUSD"
    assert state.venue == "binance"
    assert state.bids == []
    assert state.asks == []
  end

  test "applies deltas and updates book" do
    book_bin = OrderBook.new("BTCUSD", "binance")
    
    deltas = [
      %{
        side: :bid,
        action: :add,
        price: {50000_00000000, 8},
        quantity: {1_000000, 6},
        timestamp: 1640995200000000000,
        sequence: 1
      },
      %{
        side: :ask,
        action: :add,
        price: {50001_00000000, 8},
        quantity: {2_000000, 6},
        timestamp: 1640995200000000001,
        sequence: 2
      }
    ]
    
    {:ok, updated_bin} = OrderBook.apply_deltas(book_bin, deltas)
    {:ok, {best_bid, best_ask}} = OrderBook.best_bid_ask(updated_bin)
    
    assert best_bid == {50000_00000000, 8}
    assert best_ask == {50001_00000000, 8}
  end

  test "high-level interface works" do
    deltas = [
      %{
        side: :bid,
        action: :add,
        price: {49999_00000000, 8},
        quantity: {5_000000, 6},
        timestamp: 1640995200000000000,
        sequence: 1
      }
    ]
    
    {:ok, book_bin} = PocOrderbook.create_and_update("ETHUSD", "coinbase", deltas)
    summary = PocOrderbook.get_summary(book_bin)
    
    assert summary.symbol == "ETHUSD"
    assert summary.venue == "coinbase"
    assert summary.bid_levels == 1
    assert summary.ask_levels == 0
  end
end
```

## 运行说明

### 编译和测试

```bash
# 安装依赖
mix deps.get

# 编译（包括 Rust NIF）
mix compile

# 运行测试
mix test

# 运行基准测试（如果配置了）
mix run -e "PocOrderbook.Benchmark.run()"
```

### 基准测试示例

```elixir
# lib/poc_orderbook/benchmark.ex
defmodule PocOrderbook.Benchmark do
  def run do
    book_bin = PocOrderbook.Native.OrderBook.new("BTCUSD", "test")
    
    deltas = for i <- 1..1000 do
      %{
        side: if(rem(i, 2) == 0, do: :bid, else: :ask),
        action: :add,
        price: {(50000 + i) * 100000000, 8},
        quantity: {i * 1000000, 6},
        timestamp: 1640995200000000000 + i,
        sequence: i
      }
    end
    
    Benchee.run(%{
      "apply_1000_deltas" => fn -> 
        PocOrderbook.Native.OrderBook.apply_deltas(book_bin, deltas)
      end
    })
  end
end
```

这个 POC 提供了一个完整的、可运行的示例，展示了 Phoenix 与 Rust 通过 Rustler 集成的基本模式，可以作为更复杂功能开发的起点。
