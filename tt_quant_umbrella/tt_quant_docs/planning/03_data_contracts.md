# 数据契约规范

本文档定义 Phoenix 与 Rust 之间的统一二进制数据格式，确保高性能序列化和跨语言一致性。

## 设计原则

1. **零拷贝优先**：使用固定布局，支持直接内存映射
2. **小端字节序**：统一使用 little-endian，与现代 CPU 架构一致
3. **版本兼容**：支持向前/向后兼容的 schema 演进
4. **类型安全**：强类型定义，编译时检查
5. **性能优化**：批量序列化，减少分配开销

## 基础数据类型

### 原始类型定义

```rust
// Rust 侧基础类型
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct UnixNanos(pub u64);

#[repr(C)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub struct Price {
    pub value: i64,    // 定点数值 (scaled integer)
    pub precision: u8, // 小数位数 (0-18)
    pub _padding: [u8; 7], // 对齐到 8 字节
}

#[repr(C)]
#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
pub struct Quantity {
    pub value: i64,
    pub precision: u8,
    pub _padding: [u8; 7],
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum Side {
    Bid = 0,
    Ask = 1,
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum OrderType {
    Market = 0,
    Limit = 1,
    Stop = 2,
    StopLimit = 3,
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum OrderStatus {
    Pending = 0,
    Accepted = 1,
    PartiallyFilled = 2,
    Filled = 3,
    Cancelled = 4,
    Rejected = 5,
}
```

### 字符串处理

```rust
// 固定长度字符串，避免动态分配
#[repr(C)]
#[derive(Debug, Clone, PartialEq)]
pub struct FixedString<const N: usize> {
    pub data: [u8; N],
    pub len: u16,
}

impl<const N: usize> FixedString<N> {
    pub fn new(s: &str) -> Result<Self, &'static str> {
        if s.len() > N {
            return Err("String too long");
        }
        let mut data = [0u8; N];
        data[..s.len()].copy_from_slice(s.as_bytes());
        Ok(Self {
            data,
            len: s.len() as u16,
        })
    }
    
    pub fn as_str(&self) -> &str {
        std::str::from_utf8(&self.data[..self.len as usize]).unwrap()
    }
}

// 常用字符串类型别名
pub type Symbol = FixedString<16>;    // 交易标的
pub type Venue = FixedString<16>;     // 交易所
pub type OrderId = FixedString<32>;   // 订单ID
pub type TradeId = FixedString<32>;   // 成交ID
```

## 核心数据结构

### 1. 市场数据

```rust
// Tick 数据
#[repr(C)]
#[derive(Debug, Clone)]
pub struct QuoteTick {
    pub header: MessageHeader,
    pub symbol: Symbol,
    pub venue: Venue,
    pub bid_price: Price,
    pub ask_price: Price,
    pub bid_size: Quantity,
    pub ask_size: Quantity,
    pub timestamp: UnixNanos,
    pub sequence: u64,
}

#[repr(C)]
#[derive(Debug, Clone)]
pub struct TradeTick {
    pub header: MessageHeader,
    pub symbol: Symbol,
    pub venue: Venue,
    pub price: Price,
    pub size: Quantity,
    pub side: Side,
    pub timestamp: UnixNanos,
    pub sequence: u64,
    pub trade_id: TradeId,
}

// K线数据
#[repr(C)]
#[derive(Debug, Clone)]
pub struct Bar {
    pub header: MessageHeader,
    pub symbol: Symbol,
    pub venue: Venue,
    pub open: Price,
    pub high: Price,
    pub low: Price,
    pub close: Price,
    pub volume: Quantity,
    pub timestamp: UnixNanos,
    pub period_ms: u64, // 周期长度（毫秒）
}
```

### 2. 订单簿数据

```rust
// 订单簿增量
#[repr(C)]
#[derive(Debug, Clone)]
pub struct OrderBookDelta {
    pub header: MessageHeader,
    pub symbol: Symbol,
    pub venue: Venue,
    pub side: Side,
    pub action: BookAction,
    pub price: Price,
    pub size: Quantity,
    pub timestamp: UnixNanos,
    pub sequence: u64,
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum BookAction {
    Add = 0,
    Update = 1,
    Delete = 2,
    Clear = 3,
}

// 订单簿快照（变长结构）
#[repr(C)]
#[derive(Debug, Clone)]
pub struct OrderBookSnapshot {
    pub header: MessageHeader,
    pub symbol: Symbol,
    pub venue: Venue,
    pub timestamp: UnixNanos,
    pub sequence: u64,
    pub bid_count: u32,
    pub ask_count: u32,
    // 后续跟随 bid_count 个 PriceLevel，然后 ask_count 个 PriceLevel
}

#[repr(C)]
#[derive(Debug, Clone, Copy)]
pub struct PriceLevel {
    pub price: Price,
    pub size: Quantity,
}
```

### 3. 交易订单

```rust
#[repr(C)]
#[derive(Debug, Clone)]
pub struct Order {
    pub header: MessageHeader,
    pub order_id: OrderId,
    pub symbol: Symbol,
    pub venue: Venue,
    pub side: Side,
    pub order_type: OrderType,
    pub status: OrderStatus,
    pub price: Price,
    pub quantity: Quantity,
    pub filled_qty: Quantity,
    pub remaining_qty: Quantity,
    pub timestamp: UnixNanos,
    pub client_order_id: OrderId,
}

#[repr(C)]
#[derive(Debug, Clone)]
pub struct Trade {
    pub header: MessageHeader,
    pub trade_id: TradeId,
    pub order_id: OrderId,
    pub symbol: Symbol,
    pub venue: Venue,
    pub side: Side,
    pub price: Price,
    pub quantity: Quantity,
    pub timestamp: UnixNanos,
    pub commission: Price,
}
```

## 消息头与版本控制

```rust
#[repr(C)]
#[derive(Debug, Clone, Copy)]
pub struct MessageHeader {
    pub magic: u32,        // 魔数：0x54545154 ("TTQT")
    pub version: u16,      // 协议版本
    pub msg_type: u16,     // 消息类型
    pub length: u32,       // 消息总长度
    pub checksum: u32,     // CRC32 校验和
}

// 消息类型枚举
pub mod message_types {
    pub const QUOTE_TICK: u16 = 1;
    pub const TRADE_TICK: u16 = 2;
    pub const BAR: u16 = 3;
    pub const ORDER_BOOK_DELTA: u16 = 4;
    pub const ORDER_BOOK_SNAPSHOT: u16 = 5;
    pub const ORDER: u16 = 6;
    pub const TRADE: u16 = 7;
}
```

## 序列化实现

### Rust 侧序列化

```rust
use std::mem;

pub trait BinarySerialize {
    fn serialize(&self) -> Vec<u8>;
    fn deserialize(data: &[u8]) -> Result<Self, SerializationError>
    where
        Self: Sized;
}

impl BinarySerialize for QuoteTick {
    fn serialize(&self) -> Vec<u8> {
        unsafe {
            let ptr = self as *const Self as *const u8;
            let slice = std::slice::from_raw_parts(ptr, mem::size_of::<Self>());
            slice.to_vec()
        }
    }
    
    fn deserialize(data: &[u8]) -> Result<Self, SerializationError> {
        if data.len() != mem::size_of::<Self>() {
            return Err(SerializationError::InvalidLength);
        }
        
        unsafe {
            let ptr = data.as_ptr() as *const Self;
            Ok(ptr.read_unaligned())
        }
    }
}

// 批量序列化优化
pub fn serialize_batch<T: BinarySerialize>(items: &[T]) -> Vec<u8> {
    let item_size = mem::size_of::<T>();
    let total_size = 8 + items.len() * item_size; // 8字节头部
    let mut buffer = Vec::with_capacity(total_size);
    
    // 写入批次头部
    buffer.extend_from_slice(&(items.len() as u64).to_le_bytes());
    
    // 写入数据项
    for item in items {
        buffer.extend_from_slice(&item.serialize());
    }
    
    buffer
}
```

### Elixir 侧解析

```elixir
defmodule TT.Binary.Parser do
  @magic_number 0x54545154
  
  def parse_message(<<@magic_number::little-32, version::little-16, 
                     msg_type::little-16, length::little-32, 
                     checksum::little-32, payload::binary>>) do
    case verify_checksum(payload, checksum) do
      true -> parse_payload(msg_type, payload)
      false -> {:error, :checksum_mismatch}
    end
  end
  
  def parse_quote_tick(<<symbol::binary-size(16), venue::binary-size(16),
                        bid_value::little-64, bid_precision::8, _::56,
                        ask_value::little-64, ask_precision::8, _::56,
                        bid_size_value::little-64, bid_size_precision::8, _::56,
                        ask_size_value::little-64, ask_size_precision::8, _::56,
                        timestamp::little-64, sequence::little-64>>) do
    %TT.QuoteTick{
      symbol: parse_fixed_string(symbol),
      venue: parse_fixed_string(venue),
      bid_price: {bid_value, bid_precision},
      ask_price: {ask_value, ask_precision},
      bid_size: {bid_size_value, bid_size_precision},
      ask_size: {ask_size_value, ask_size_precision},
      timestamp: timestamp,
      sequence: sequence
    }
  end
  
  defp parse_fixed_string(binary) do
    case :binary.split(binary, <<0>>) do
      [str, _] -> str
      [str] -> str
    end
  end
  
  defp verify_checksum(payload, expected) do
    :erlang.crc32(payload) == expected
  end
end
```

## 性能优化策略

### 1. 内存池化

```rust
use std::sync::Arc;
use parking_lot::Mutex;

pub struct MessagePool<T> {
    pool: Arc<Mutex<Vec<Box<T>>>>,
    max_size: usize,
}

impl<T: Default> MessagePool<T> {
    pub fn new(max_size: usize) -> Self {
        Self {
            pool: Arc::new(Mutex::new(Vec::with_capacity(max_size))),
            max_size,
        }
    }
    
    pub fn get(&self) -> Box<T> {
        let mut pool = self.pool.lock();
        pool.pop().unwrap_or_else(|| Box::new(T::default()))
    }
    
    pub fn put(&self, mut item: Box<T>) {
        let mut pool = self.pool.lock();
        if pool.len() < self.max_size {
            // 重置对象状态
            *item = T::default();
            pool.push(item);
        }
    }
}
```

### 2. 批量处理

```rust
// 批量消息容器
#[repr(C)]
pub struct MessageBatch<T> {
    pub count: u32,
    pub capacity: u32,
    pub data: *mut T,
}

impl<T> MessageBatch<T> {
    pub fn new(capacity: usize) -> Self {
        let layout = std::alloc::Layout::array::<T>(capacity).unwrap();
        let ptr = unsafe { std::alloc::alloc(layout) as *mut T };
        
        Self {
            count: 0,
            capacity: capacity as u32,
            data: ptr,
        }
    }
    
    pub fn push(&mut self, item: T) -> Result<(), &'static str> {
        if self.count >= self.capacity {
            return Err("Batch full");
        }
        
        unsafe {
            self.data.add(self.count as usize).write(item);
        }
        self.count += 1;
        Ok(())
    }
    
    pub fn as_slice(&self) -> &[T] {
        unsafe {
            std::slice::from_raw_parts(self.data, self.count as usize)
        }
    }
}
```

## 错误处理

```rust
#[derive(Debug, thiserror::Error)]
pub enum SerializationError {
    #[error("Invalid message length: expected {expected}, got {actual}")]
    InvalidLength { expected: usize, actual: usize },
    
    #[error("Checksum mismatch")]
    ChecksumMismatch,
    
    #[error("Unknown message type: {0}")]
    UnknownMessageType(u16),
    
    #[error("Invalid string encoding")]
    InvalidString,
}
```

这套数据契约规范确保了高性能的跨语言数据交换，支持零拷贝操作和批量处理，为量化交易系统提供了坚实的数据基础。
