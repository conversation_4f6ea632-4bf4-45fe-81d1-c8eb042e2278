# Phoenix 与 Rust（Rustler）职责划分与架构建议

本文给出使用 Phoenix 重构 NautilusTrader，并以 Rust（通过 Rustler NIF）实现性能关键路径的职责划分、接口边界与落地建议。目标是在保证撮合/风控/数据处理链路低延迟与确定性的前提下，发挥 BEAM 在并发、容错、可观测与分布式编排上的优势。

## 设计目标
- 低延迟与确定性：纳秒时间、定点数，热路径尽量减少跨语言开销
- 高吞吐与可扩展：多标的、多账户、分布式扩展，热点分片
- 可观测与可运维：可视化监控、告警、回放、配置审计、热升级
- 容错与隔离：失败自愈、故障隔离、防止单点阻塞 BEAM 调度器
- 统一策略体验：回测与实盘代码一致，环境切换最小化

## 划分原则
1. 热路径尽量 Rust：数据解码→标准化→盘口重建→信号/执行算法→风险检查→签名→下单
2. Phoenix 负责控制面与人机交互：编排、配置、监控、权限、审计、可视化与外部 API 网关
3. 边界以“不可变二进制事件流”为主：减少 term 编解码与 GC 压力
4. NIF 谨慎：长耗时/CPU 绑定函数使用 `DirtyCpu`/`DirtyIo`，或在 Rust 侧自管线程并异步回调
5. 存储分层：
   - Tick/行情/回测结果等大数据：Rust + Arrow/Parquet 列存
   - 元数据/配置/运行状态：Phoenix + Ecto（Postgres）
6. 失败优先隔离：极重 CPU 任务如需更强隔离，可升级为 Port/外部进程（即使默认采用 Rustler）

## 推荐职责划分

### 由 Phoenix/Elixir 负责（控制面/交互/编排）
- 集群与编排
  - 分布式节点、分区/分片调度（Registry/PG + DynamicSupervisor）
  - 进程拓扑、心跳、自动拉起、版本发布（Distillery/Release）
- 管理与网关
  - REST/GraphQL API（Phoenix Controller/Absinthe）
  - WebSocket/LiveView 实时看板（账户、持仓、订单、延迟、吞吐）
  - 认证鉴权（Pow/Guardian/OAuth2）、多租户
- 任务与数据服务
  - 作业调度与重试（Oban）：回测提交、数据导入/转换、清算报表生成
  - 配置中心（Ecto + Postgres）：账户、路由、风险参数、策略配置、实验版本
  - 事件总线（Phoenix.PubSub / Broadway）：非热路径的系统事件广播
- 连接管理（建议 Elixir 驱动，Rust 解析）：
  - 连接生命周期、重连、心跳、速率限制、流量整形
  - 具体协议帧的签名/校验/压缩/解压可下沉 Rust NIF
- 可观测性
  - Telemetry/Metrics/Logger/Tracing，SLO 仪表板与告警

### 由 Rust（通过 Rustler）负责（数据与计算热路径）
- 市场数据处理
  - 二进制解码（JSON/CBOR/Protobuf/自定义）与标准化
  - 增量盘口重建（LOB）、聚合（Bar）、微结构特征
  - 时间戳校正、时区/会话边界处理
- 执行与风险
  - 执行算法（TWAP/VWAP/POV/冰山等）
  - 预交易风险（最大仓位、订单限额、价格带、保证金/杠杆检查）
  - 账户净值/保证金模型、仓位合并与净额计算
  - 订单路由、签名与序列号管理（HMAC/ECDSA 等）
- 回测与仿真
  - 事件驱动仿真内核、撮合模型、滑点与手续费
  - 指标/因子计算（技术指标库、统计量、风险指标）
- 序列化与持久化（数据面）
  - Arrow/Parquet 读写、列式压缩、分区分桶
  - 高性能查询下推（时间/标的/字段谓词）
- 基础类型与工具
  - 纳秒时间、定点 Decimal、标的/订单/成交/盘口等领域模型

## 接口边界与数据契约
- 事件形态：以不可变二进制（Elixir Binary <-> Rust OwnedBinary）为主，减少 term alloc
- NIF 形态：
  - 小而纯（pure）的函数可直接同步返回（非阻塞）
  - 重 CPU/IO 的函数标注 `#[rustler::nif(schedule = "DirtyCpu")]` 或 `DirtyIo`
  - 连续流式任务：在 Rust 内部管理线程/任务队列，通过回调向 BEAM 发送事件（`Env::send`）
- 典型 NIF 接口模块建议（Elixir 侧命名）：
  - `TT.Native.OrderBook`：`apply_deltas/2`，`best_bid_ask/1`
  - `TT.Native.MarketData`：`decode/1`（批量），`normalize/1`
  - `TT.Native.Execution`：`route/2`，`sign/2`，`risk_check/2`
  - `TT.Native.Backtest`：`run/2`（回放数据 + 配置 → 度量/轨迹）
  - `TT.Native.DataCatalog`：`write_parquet/2`，`scan_parquet/2`

### NIF 调用示意
- Elixir

```elixir
# lib/tt/native/order_book.ex
defmodule TT.Native.OrderBook do
  use Rustler, otp_app: :tt, crate: "tt_order_book"
  def apply_deltas(_book_bin, _deltas_bin), do: :erlang.nif_error(:nif_not_loaded)
end
```

- Rust

```rust
#[rustler::nif(schedule = "DirtyCpu")]
pub fn apply_deltas(book: Binary, deltas: Binary) -> NifResult<OwnedBinary> {
    // 解析二进制 -> 应用增量 -> 返回新书本二进制
}
```

## 并发与事件流模型
- 热路径（行情→信号→风控→下单）：Rust 内部以无锁环形队列/跨线程通道组织，尽量单边界返回关键结果（如“应发订单”）
- Phoenix 获取“必要可观测事件”：
  - 聚合后的指标、PnL、风险状态、失败告警、心跳
  - 重要状态快照以节流频率上报，降低 UI/网络压力
- 对外接口（UI/API/第三方系统）：通过 Phoenix PubSub/WebSocket 推送，必要时由 Rust 侧做限速/采样

## 存储与数据管理
- 数据面：Rust + Arrow/Parquet（列式、压缩、分区：date/venue/symbol）
- 元数据：Ecto + Postgres（策略配置、实验记录、风控参数、运行状态、审计日志）
- 统一数据契约：采用 Arrow Schema/FlatBuffer/自定义小端布局之一；尽量避免跨层重复解析

## 容错与安全
- NIF 安全：
  - 标注 Dirty 调度器，避免阻塞 BEAM；对 panic 做边界捕获
  - 资源对象（ResourceArc）封装 long-lived 状态；显式生命周期
- 进程隔离：
  - 极重计算或第三方不可信库可通过 Port/外部进程隔离（保留升级选项）
- 配额与限流：
  - Phoenix 管控请求速率/策略并发；Rust 执行侧本地队列回压与丢弃策略
- 灾备：
  - Phoenix 侧状态快照、回放；数据面定期 checkpoint

## Phoenix 应用结构建议
```
apps/tt_web       # Phoenix (UI/API/LiveView)
apps/tt_control   # 编排与调度（策略/账户/风控配置）
apps/tt_stream    # 事件路由（PubSub/Broadway 接多源）
apps/tt_data      # 元数据（Ecto）与数据目录控制面
apps/tt_native    # NIF 壳模块（Elixir）与 Rust crates 工作区
```

Rust 工作区（示例）：
```
rust/
  Cargo.toml (workspace)
  crates/
    order_book/
    market_data/
    execution/
    risk/
    backtest/
    datacatalog/
```

## 性能与延迟预算（参考）
- 解码与标准化：< 50µs/批
- L2 更新与指标：< 20µs/增量
- 风控与签名：< 30µs/单
- BEAM 边界：一次跨界 < 5–10µs；批量化优先，减少跨界次数

## 迁移路径（里程碑）
1) 最小可用架构（1–2 周）
- Phoenix 项目骨架、Ecto 元数据、LiveView 仪表板雏形
- Rustler 打通：`TT.Native.OrderBook.apply_deltas/2` POC

2) 市场数据与盘口（2–4 周）
- Rust 解码/标准化/盘口重建，Phoenix 展示与回放

3) 执行与风控（3–6 周）
- 执行算法与预交易风险 NIF，签名、路由与发送链路

4) 回测内核与数据目录（4–8 周）
- Rust 回测引擎 + Parquet 数据目录，Phoenix 侧提交/查看/对比

5) 连接器与稳定性（持续）
- 各交易所协议适配，端到端延迟与稳定性压测

## 风险与替代方案
- 大型 NIF 的稳定性风险：对极重/不可信逻辑可采用 Port/外部微服务（gRPC/NATS）
- GC/二进制复制：采用 zero-copy 二进制布局与批量接口
- 多语言一致性：用统一 schema（Arrow/FlatBuffers）减少“歧义解析”

## 小结
- Phoenix 负责“看得见的世界”：编排、可视化、网关、可观测与元数据
- Rust 负责“跑得快的世界”：行情、盘口、执行、风控、回测与大数据列存
- 通过 Rustler 建立窄而稳定的二进制接口，既保留低延迟，又不牺牲 BEAM 的容错与可运维性

