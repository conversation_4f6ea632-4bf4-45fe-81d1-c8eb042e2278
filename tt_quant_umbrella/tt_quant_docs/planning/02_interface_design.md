# 接口原型与示例代码

本文档定义 Phoenix 与 Rust（Rustler）之间的具体接口规范，包含 NIF 模块定义、函数签名、数据结构 schema 和错误处理机制。

## 核心数据类型定义

### 时间与精度类型

```rust
// Rust 侧基础类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Copy)]
pub struct UnixNanos(pub u64);

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Copy)]
pub struct Price {
    pub value: i64,    // 定点数值
    pub precision: u8, // 小数位数
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Quantity {
    pub value: i64,
    pub precision: u8,
}
```

```elixir
# Elixir 侧类型定义
defmodule TT.Types do
  @type unix_nanos :: non_neg_integer()
  @type price :: {value :: integer(), precision :: non_neg_integer()}
  @type quantity :: {value :: integer(), precision :: non_neg_integer()}
  @type symbol :: String.t()
  @type venue :: String.t()
end
```

### 订单簿数据结构

```rust
// Rust 侧订单簿增量
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct OrderBookDelta {
    pub symbol: String,
    pub venue: String,
    pub timestamp: UnixNanos,
    pub sequence: u64,
    pub side: Side,
    pub action: Action,
    pub price: Price,
    pub quantity: Quantity,
}

#[derive(Debug, Clone, Copy)]
pub enum Side { Bid, Ask }

#[derive(Debug, Clone, Copy)]
pub enum Action { Add, Update, Delete }

// 订单簿快照
#[derive(Debug, Clone)]
pub struct OrderBookSnapshot {
    pub symbol: String,
    pub venue: String,
    pub timestamp: UnixNanos,
    pub sequence: u64,
    pub bids: Vec<(Price, Quantity)>,
    pub asks: Vec<(Price, Quantity)>,
}
```

## NIF 模块接口定义

### 1. OrderBook 模块

```elixir
# lib/tt/native/order_book.ex
defmodule TT.Native.OrderBook do
  use Rustler, otp_app: :tt_native, crate: "tt_order_book"

  @type book_binary :: binary()
  @type deltas_binary :: binary()
  @type result :: {:ok, book_binary()} | {:error, String.t()}

  @doc "应用增量更新到订单簿"
  @spec apply_deltas(book_binary(), deltas_binary()) :: result()
  def apply_deltas(_book_bin, _deltas_bin), do: :erlang.nif_error(:nif_not_loaded)

  @doc "获取最优买卖价"
  @spec best_bid_ask(book_binary()) :: {:ok, {TT.Types.price(), TT.Types.price()}} | {:error, String.t()}
  def best_bid_ask(_book_bin), do: :erlang.nif_error(:nif_not_loaded)

  @doc "获取指定深度的盘口"
  @spec get_depth(book_binary(), pos_integer()) :: {:ok, {[{TT.Types.price(), TT.Types.quantity()}], [{TT.Types.price(), TT.Types.quantity()}]}} | {:error, String.t()}
  def get_depth(_book_bin, _depth), do: :erlang.nif_error(:nif_not_loaded)

  @doc "创建空订单簿"
  @spec new(TT.Types.symbol(), TT.Types.venue()) :: book_binary()
  def new(_symbol, _venue), do: :erlang.nif_error(:nif_not_loaded)
end
```

```rust
// rust/crates/order_book/src/lib.rs
use rustler::{Binary, Env, NifResult, OwnedBinary, ResourceArc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone)]
pub struct OrderBookResource {
    pub inner: OrderBook,
}

#[rustler::nif(schedule = "DirtyCpu")]
pub fn apply_deltas(book_bin: Binary, deltas_bin: Binary) -> NifResult<OwnedBinary> {
    let book: OrderBookSnapshot = bincode::deserialize(book_bin.as_slice())
        .map_err(|e| rustler::Error::Term(Box::new(format!("Failed to deserialize book: {}", e))))?;
    
    let deltas: Vec<OrderBookDelta> = bincode::deserialize(deltas_bin.as_slice())
        .map_err(|e| rustler::Error::Term(Box::new(format!("Failed to deserialize deltas: {}", e))))?;
    
    let mut updated_book = book;
    for delta in deltas {
        apply_single_delta(&mut updated_book, delta)?;
    }
    
    let serialized = bincode::serialize(&updated_book)
        .map_err(|e| rustler::Error::Term(Box::new(format!("Failed to serialize result: {}", e))))?;
    
    let mut binary = OwnedBinary::new(serialized.len()).unwrap();
    binary.as_mut_slice().copy_from_slice(&serialized);
    Ok(binary)
}

#[rustler::nif]
pub fn best_bid_ask(book_bin: Binary) -> NifResult<(Option<(i64, u8)>, Option<(i64, u8)>)> {
    let book: OrderBookSnapshot = bincode::deserialize(book_bin.as_slice())
        .map_err(|e| rustler::Error::Term(Box::new(format!("Failed to deserialize: {}", e))))?;
    
    let best_bid = book.bids.first().map(|(p, _)| (p.value, p.precision));
    let best_ask = book.asks.first().map(|(p, _)| (p.value, p.precision));
    
    Ok((best_bid, best_ask))
}

#[rustler::nif]
pub fn new(symbol: String, venue: String) -> NifResult<OwnedBinary> {
    let book = OrderBookSnapshot {
        symbol,
        venue,
        timestamp: UnixNanos(0),
        sequence: 0,
        bids: Vec::new(),
        asks: Vec::new(),
    };
    
    let serialized = bincode::serialize(&book)
        .map_err(|e| rustler::Error::Term(Box::new(format!("Serialization failed: {}", e))))?;
    
    let mut binary = OwnedBinary::new(serialized.len()).unwrap();
    binary.as_mut_slice().copy_from_slice(&serialized);
    Ok(binary)
}

fn apply_single_delta(book: &mut OrderBookSnapshot, delta: OrderBookDelta) -> NifResult<()> {
    // 实现增量应用逻辑
    match (delta.side, delta.action) {
        (Side::Bid, Action::Add) => {
            book.bids.push((delta.price, delta.quantity));
            book.bids.sort_by(|a, b| b.0.value.cmp(&a.0.value)); // 降序
        },
        (Side::Ask, Action::Add) => {
            book.asks.push((delta.price, delta.quantity));
            book.asks.sort_by(|a, b| a.0.value.cmp(&b.0.value)); // 升序
        },
        // ... 其他操作
        _ => {}
    }
    book.sequence = delta.sequence;
    book.timestamp = delta.timestamp;
    Ok(())
}

rustler::init!("Elixir.TT.Native.OrderBook", [apply_deltas, best_bid_ask, new]);
```

### 2. MarketData 模块

```elixir
# lib/tt/native/market_data.ex
defmodule TT.Native.MarketData do
  use Rustler, otp_app: :tt_native, crate: "tt_market_data"

  @type raw_data :: binary()
  @type normalized_data :: binary()
  @type decode_result :: {:ok, [normalized_data()]} | {:error, String.t()}

  @doc "批量解码原始市场数据"
  @spec decode_batch(raw_data(), String.t()) :: decode_result()
  def decode_batch(_raw_data, _format), do: :erlang.nif_error(:nif_not_loaded)

  @doc "标准化市场数据格式"
  @spec normalize(binary()) :: {:ok, normalized_data()} | {:error, String.t()}
  def normalize(_data), do: :erlang.nif_error(:nif_not_loaded)
end
```

```rust
// rust/crates/market_data/src/lib.rs
#[rustler::nif(schedule = "DirtyCpu")]
pub fn decode_batch(raw_data: Binary, format: String) -> NifResult<Vec<OwnedBinary>> {
    let results = match format.as_str() {
        "json" => decode_json_batch(raw_data.as_slice())?,
        "cbor" => decode_cbor_batch(raw_data.as_slice())?,
        "protobuf" => decode_protobuf_batch(raw_data.as_slice())?,
        _ => return Err(rustler::Error::Term(Box::new("Unsupported format"))),
    };
    
    let mut binaries = Vec::new();
    for result in results {
        let serialized = bincode::serialize(&result)
            .map_err(|e| rustler::Error::Term(Box::new(format!("Serialization failed: {}", e))))?;
        let mut binary = OwnedBinary::new(serialized.len()).unwrap();
        binary.as_mut_slice().copy_from_slice(&serialized);
        binaries.push(binary);
    }
    
    Ok(binaries)
}

rustler::init!("Elixir.TT.Native.MarketData", [decode_batch, normalize]);
```

### 3. Execution 模块

```elixir
# lib/tt/native/execution.ex
defmodule TT.Native.Execution do
  use Rustler, otp_app: :tt_native, crate: "tt_execution"

  @type order_binary :: binary()
  @type risk_params :: binary()
  @type route_result :: {:ok, binary()} | {:error, String.t()}
  @type risk_result :: :ok | {:error, String.t()}

  @doc "订单路由与签名"
  @spec route_and_sign(order_binary(), String.t()) :: route_result()
  def route_and_sign(_order, _venue), do: :erlang.nif_error(:nif_not_loaded)

  @doc "预交易风险检查"
  @spec risk_check(order_binary(), risk_params()) :: risk_result()
  def risk_check(_order, _params), do: :erlang.nif_error(:nif_not_loaded)
end
```

## 错误处理机制

```rust
// 统一错误类型
#[derive(Debug, thiserror::Error)]
pub enum TTError {
    #[error("Serialization error: {0}")]
    Serialization(#[from] bincode::Error),
    
    #[error("Invalid data: {0}")]
    InvalidData(String),
    
    #[error("Risk check failed: {0}")]
    RiskCheckFailed(String),
    
    #[error("Network error: {0}")]
    Network(String),
}

impl From<TTError> for rustler::Error {
    fn from(err: TTError) -> Self {
        rustler::Error::Term(Box::new(err.to_string()))
    }
}
```

```elixir
# Elixir 侧错误处理
defmodule TT.Native.Error do
  @type error_reason :: 
    :serialization_error |
    :invalid_data |
    :risk_check_failed |
    :network_error |
    :unknown_error

  @spec parse_error(String.t()) :: error_reason()
  def parse_error(error_msg) do
    cond do
      String.contains?(error_msg, "Serialization") -> :serialization_error
      String.contains?(error_msg, "Invalid data") -> :invalid_data
      String.contains?(error_msg, "Risk check") -> :risk_check_failed
      String.contains?(error_msg, "Network") -> :network_error
      true -> :unknown_error
    end
  end
end
```

## 资源管理

```rust
// 长生命周期资源管理
#[derive(Debug)]
pub struct OrderBookManager {
    books: std::collections::HashMap<String, OrderBook>,
}

impl OrderBookManager {
    pub fn new() -> Self {
        Self {
            books: std::collections::HashMap::new(),
        }
    }
}

// 注册资源类型
fn on_load(env: Env, _info: Term) -> bool {
    rustler::resource!(OrderBookManager, env);
    true
}

#[rustler::nif]
pub fn create_manager() -> ResourceArc<OrderBookManager> {
    ResourceArc::new(OrderBookManager::new())
}

#[rustler::nif]
pub fn add_book(manager: ResourceArc<OrderBookManager>, symbol: String, venue: String) -> NifResult<()> {
    // 使用 ResourceArc 管理长生命周期状态
    Ok(())
}
```

## 性能优化建议

1. **批量处理**：尽量批量传递数据，减少 NIF 调用次数
2. **零拷贝**：使用 `Binary` 和 `OwnedBinary` 避免不必要的内存复制
3. **Dirty 调度器**：CPU 密集型操作使用 `DirtyCpu`，IO 操作使用 `DirtyIo`
4. **资源池化**：使用 `ResourceArc` 管理可复用的重型对象
5. **错误边界**：在 Rust 侧捕获所有 panic，避免崩溃 BEAM

这套接口设计确保了类型安全、性能优化和错误隔离，为后续的具体实现提供了清晰的规范。
