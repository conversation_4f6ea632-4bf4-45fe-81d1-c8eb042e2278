# Nautilus Trader - Rust 架构模式与最佳实践

## 📐 架构设计模式

### 1. 分层架构 (Layered Architecture)

Nautilus Trader 的 Rust 代码采用清晰的分层设计：

```
┌─────────────────────────────────────────┐
│         Python Strategies Layer         │  <- 策略层 (Python)
├─────────────────────────────────────────┤
│            PyO3 Bindings                │  <- 语言绑定层
├─────────────────────────────────────────┤
│         Application Services            │  <- 应用服务层
│  (Trading, Portfolio, Risk, Analysis)   │
├─────────────────────────────────────────┤
│          Domain Model Layer             │  <- 领域模型层
│    (Orders, Positions, Instruments)     │
├─────────────────────────────────────────┤
│         Infrastructure Layer            │  <- 基础设施层
│   (Network, Persistence, Messaging)     │
├─────────────────────────────────────────┤
│            Core Utilities               │  <- 核心工具层
│      (Time, UUID, Math, Parsing)        │
└─────────────────────────────────────────┘
```

### 2. 六边形架构 (Hexagonal Architecture)

```rust
// 核心域 - 不依赖外部
pub mod domain {
    pub struct Order {
        id: OrderId,
        instrument_id: InstrumentId,
        // 纯业务逻辑
    }
}

// 端口 - 定义接口
pub trait ExecutionPort {
    async fn submit_order(&self, order: Order) -> Result<()>;
}

// 适配器 - 实现具体交易所
pub struct BinanceAdapter;
impl ExecutionPort for BinanceAdapter {
    async fn submit_order(&self, order: Order) -> Result<()> {
        // 转换为 Binance API 格式
    }
}
```

### 3. 事件驱动架构 (Event-Driven Architecture)

```rust
// 事件定义
#[derive(Clone, Debug)]
pub enum TradingEvent {
    OrderSubmitted(OrderSubmittedEvent),
    OrderFilled(OrderFilledEvent),
    PositionOpened(PositionOpenedEvent),
}

// 事件总线
pub struct EventBus {
    subscribers: HashMap<EventType, Vec<Box<dyn EventHandler>>>,
}

impl EventBus {
    pub fn publish(&self, event: TradingEvent) {
        if let Some(handlers) = self.subscribers.get(&event.type_id()) {
            for handler in handlers {
                handler.handle(event.clone());
            }
        }
    }
}
```

## 🎨 设计模式实践

### 1. 新类型模式 (Newtype Pattern)

用于类型安全和零成本抽象：

```rust
// 防止混淆不同的 ID 类型
pub struct OrderId(UUID4);
pub struct TradeId(UUID4);
pub struct PositionId(UUID4);

// 防止混淆数值类型
pub struct Price(i64);
pub struct Quantity(u64);
pub struct Money(i64);

impl Price {
    pub fn new(value: f64, precision: u8) -> Self {
        let raw = (value * 10_f64.powi(precision as i32)) as i64;
        Self(raw)
    }
}
```

### 2. 类型状态模式 (Typestate Pattern)

用类型系统编码状态机，编译时防止非法状态转换：

```rust
// 订单状态
pub struct Initialized;
pub struct Submitted;
pub struct Filled;
pub struct Canceled;

pub struct Order<S> {
    id: OrderId,
    data: OrderData,
    _state: PhantomData<S>,
}

// 只有已提交的订单才能填充
impl Order<Submitted> {
    pub fn fill(self, fill_price: Price) -> Order<Filled> {
        Order {
            id: self.id,
            data: self.data.with_fill(fill_price),
            _state: PhantomData,
        }
    }
}

// 只有已提交的订单才能取消
impl Order<Submitted> {
    pub fn cancel(self) -> Order<Canceled> {
        Order {
            id: self.id,
            data: self.data,
            _state: PhantomData,
        }
    }
}
```

### 3. Builder 模式

用于构建复杂对象：

```rust
#[derive(Builder)]
#[builder(pattern = "owned")]
pub struct BacktestConfig {
    #[builder(default = "UTC")]
    pub timezone: Tz,
    
    #[builder(setter(into))]
    pub start_time: DateTime<Utc>,
    
    #[builder(setter(into))]
    pub end_time: DateTime<Utc>,
    
    #[builder(default = "false")]
    pub streaming: bool,
    
    #[builder(default = "1_000_000.0")]
    pub starting_cash: f64,
}

// 使用
let config = BacktestConfigBuilder::default()
    .start_time("2024-01-01T00:00:00Z")
    .end_time("2024-12-31T23:59:59Z")
    .streaming(true)
    .build()?;
```

### 4. 策略模式 (Strategy Pattern)

用于可替换的算法实现：

```rust
pub trait FillModel {
    fn calculate_fill_price(&self, order: &Order, market: &MarketData) -> Price;
}

pub struct MarketFillModel;
impl FillModel for MarketFillModel {
    fn calculate_fill_price(&self, order: &Order, market: &MarketData) -> Price {
        market.best_ask() // 市价成交
    }
}

pub struct SlippageFillModel {
    slippage_bps: u32,
}
impl FillModel for SlippageFillModel {
    fn calculate_fill_price(&self, order: &Order, market: &MarketData) -> Price {
        // 考虑滑点的成交模型
        let base_price = market.best_ask();
        base_price * (1.0 + self.slippage_bps as f64 / 10000.0)
    }
}
```

## 🔧 Rust 特色模式

### 1. 内部可变性 (Interior Mutability)

使用 `RefCell` 或原子类型实现：

```rust
pub struct Cache {
    data: RefCell<HashMap<String, Value>>,
}

impl Cache {
    pub fn get(&self, key: &str) -> Option<Value> {
        self.data.borrow().get(key).cloned()
    }
    
    pub fn set(&self, key: String, value: Value) {
        self.data.borrow_mut().insert(key, value);
    }
}

// 原子操作版本
pub struct AtomicCache {
    version: AtomicU64,
    data: RwLock<HashMap<String, Value>>,
}
```

### 2. RAII 模式

资源获取即初始化：

```rust
pub struct TradingSession {
    connection: WebSocketConnection,
    _heartbeat_task: JoinHandle<()>,
}

impl TradingSession {
    pub async fn new(url: &str) -> Result<Self> {
        let connection = WebSocketConnection::connect(url).await?;
        
        // 自动启动心跳任务
        let heartbeat_task = tokio::spawn(async move {
            loop {
                tokio::time::sleep(Duration::from_secs(30)).await;
                // 发送心跳
            }
        });
        
        Ok(Self {
            connection,
            _heartbeat_task: heartbeat_task,
        })
    }
}

// Drop trait 确保清理
impl Drop for TradingSession {
    fn drop(&mut self) {
        self._heartbeat_task.abort();
        // 连接会自动关闭
    }
}
```

### 3. 错误处理模式

使用自定义错误类型：

```rust
#[derive(Debug, thiserror::Error)]
pub enum TradingError {
    #[error("Invalid order: {0}")]
    InvalidOrder(String),
    
    #[error("Insufficient balance: required {required}, available {available}")]
    InsufficientBalance {
        required: Money,
        available: Money,
    },
    
    #[error("Network error: {0}")]
    Network(#[from] NetworkError),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
}

// Result 类型别名
pub type TradingResult<T> = Result<T, TradingError>;
```

## 🚀 性能优化模式

### 1. 零拷贝设计

```rust
// 避免克隆，使用引用
pub struct MarketDataView<'a> {
    bids: &'a [Level],
    asks: &'a [Level],
    timestamp: UnixNanos,
}

impl<'a> MarketDataView<'a> {
    pub fn best_bid(&self) -> Option<&Level> {
        self.bids.first()
    }
}
```

### 2. 对象池模式

```rust
pub struct OrderPool {
    pool: Vec<Box<Order>>,
    capacity: usize,
}

impl OrderPool {
    pub fn acquire(&mut self) -> Box<Order> {
        self.pool.pop().unwrap_or_else(|| Box::new(Order::default()))
    }
    
    pub fn release(&mut self, mut order: Box<Order>) {
        if self.pool.len() < self.capacity {
            order.reset(); // 重置状态
            self.pool.push(order);
        }
    }
}
```

### 3. 批处理模式

```rust
pub struct BatchProcessor {
    buffer: Vec<Event>,
    capacity: usize,
}

impl BatchProcessor {
    pub fn push(&mut self, event: Event) {
        self.buffer.push(event);
        
        if self.buffer.len() >= self.capacity {
            self.flush();
        }
    }
    
    fn flush(&mut self) {
        // 批量处理
        let events = std::mem::take(&mut self.buffer);
        self.process_batch(events);
    }
}
```

## 🔒 并发模式

### 1. Actor 模式

```rust
pub struct OrderManager {
    receiver: mpsc::Receiver<OrderCommand>,
    orders: HashMap<OrderId, Order>,
}

impl OrderManager {
    pub async fn run(mut self) {
        while let Some(cmd) = self.receiver.recv().await {
            match cmd {
                OrderCommand::Submit(order) => {
                    self.orders.insert(order.id(), order);
                }
                OrderCommand::Cancel(id) => {
                    self.orders.remove(&id);
                }
                OrderCommand::Query(id, response_tx) => {
                    let order = self.orders.get(&id).cloned();
                    let _ = response_tx.send(order);
                }
            }
        }
    }
}
```

### 2. 无锁数据结构

```rust
use dashmap::DashMap;

pub struct ConcurrentOrderBook {
    bids: Arc<DashMap<Price, Vec<Order>>>,
    asks: Arc<DashMap<Price, Vec<Order>>>,
}

impl ConcurrentOrderBook {
    pub fn add_order(&self, order: Order) {
        let book = match order.side {
            OrderSide::Buy => &self.bids,
            OrderSide::Sell => &self.asks,
        };
        
        book.entry(order.price)
            .or_insert_with(Vec::new)
            .push(order);
    }
}
```

## 📝 最佳实践总结

### 1. 类型系统利用

- ✅ 使用新类型模式增加类型安全
- ✅ 用类型状态模式编码状态机
- ✅ 利用枚举表达业务逻辑

### 2. 错误处理

- ✅ 使用 `Result<T, E>` 而非 panic
- ✅ 定义领域特定的错误类型
- ✅ 使用 `thiserror` 简化错误定义

### 3. 性能优化

- ✅ 优先使用栈分配
- ✅ 避免不必要的克隆
- ✅ 使用 `Cow` 实现写时复制
- ✅ 批处理提高吞吐量

### 4. 并发安全

- ✅ 优先使用消息传递
- ✅ 避免共享可变状态
- ✅ 使用原子类型处理简单共享
- ✅ Actor 模式处理复杂状态

### 5. 代码组织

- ✅ 模块化设计，职责单一
- ✅ 公共 API 最小化
- ✅ 文档完善，示例充分
- ✅ 测试覆盖率高

## 🎯 架构决策记录 (ADR)

### ADR-001: 使用定点数而非浮点数

**背景**: 金融计算需要精确性

**决策**: 使用 i64/i128 定点数

**后果**: 
- ✅ 无浮点误差
- ✅ 确定性计算
- ❌ 需要转换逻辑

### ADR-002: Rust + Python 混合架构

**背景**: 需要高性能和易用性

**决策**: Rust 核心 + Python 策略

**后果**:
- ✅ 关键路径性能优异
- ✅ 策略开发便捷
- ❌ 增加系统复杂度

### ADR-003: 使用 Tokio 异步运行时

**背景**: 需要高并发 I/O

**决策**: 采用 Tokio

**后果**:
- ✅ 高性能异步 I/O
- ✅ 生态系统丰富
- ❌ 学习曲线陡峭

---

*本文档展示了 Nautilus Trader 中的架构模式和最佳实践*
