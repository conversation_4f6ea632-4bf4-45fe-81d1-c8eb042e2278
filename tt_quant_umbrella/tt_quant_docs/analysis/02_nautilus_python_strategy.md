# Nautilus Trader - Python 策略框架设计分析

## 📌 概述

Nautilus Trader 的 Python 策略框架提供了一个强大而灵活的交易策略开发环境。策略层运行在 Python 中，享受语言的灵活性，同时底层的性能关键部分由 Rust 实现。

## 🏗️ 策略架构

### 整体结构

```
┌──────────────────────────────────────┐
│       User Strategy (Python)         │
├──────────────────────────────────────┤
│      Strategy Base Class (Cython)    │
├──────────────────────────────────────┤
│         Actor Framework               │
├──────────────────────────────────────┤
│      Trading Components               │
│  (OrderFactory, Portfolio, Cache)    │
├──────────────────────────────────────┤
│       Execution Engine (Rust)        │
└──────────────────────────────────────┘
```

## 🎯 核心组件

### 1. Strategy 基类

```python
# nautilus_trader/trading/strategy.pyx

cdef class Strategy(Actor):
    """
    所有交易策略的基类
    """
    def __init__(self, config: StrategyConfig | None = None):
        # 策略配置
        self.config = config
        self.oms_type = config.oms_type  # HEDGING 或 NETTING
        
        # 核心组件（注册后初始化）
        self.clock = None         # 时钟
        self.cache = None         # 缓存
        self.portfolio = None     # 投资组合
        self.order_factory = None # 订单工厂
        
        # 订单管理
        self._manager = None      # 订单管理器
```

### 2. 策略生命周期

```python
class MyStrategy(Strategy):
    """用户自定义策略"""
    
    def on_start(self) -> None:
        """策略启动时调用"""
        # 订阅市场数据
        self.subscribe_quote_ticks(self.instrument_id)
        self.subscribe_order_book_deltas(self.instrument_id)
        
    def on_stop(self) -> None:
        """策略停止时调用"""
        # 取消所有订单
        self.cancel_all_orders(self.instrument_id)
        
    def on_reset(self) -> None:
        """策略重置时调用"""
        # 重置内部状态
        self.indicators.clear()
        
    def on_resume(self) -> None:
        """策略恢复时调用"""
        # 恢复订阅
        self.resubscribe_data()
```

## 📊 数据处理

### 1. 市场数据订阅

```python
class DataDrivenStrategy(Strategy):
    
    def on_start(self):
        # 订阅不同类型的数据
        
        # 1. Tick 数据
        self.subscribe_quote_ticks(
            instrument_id=self.instrument_id,
            handler=self.on_quote_tick,
        )
        
        # 2. K线数据
        self.subscribe_bars(
            bar_type=BarType.from_str("BTCUSDT.BINANCE-1-MINUTE-LAST"),
            handler=self.on_bar,
        )
        
        # 3. 订单簿快照
        self.subscribe_order_book_snapshots(
            instrument_id=self.instrument_id,
            book_type=BookType.L2_MBP,
            depth=20,
            handler=self.on_order_book,
        )
        
        # 4. 成交数据
        self.subscribe_trade_ticks(
            instrument_id=self.instrument_id,
            handler=self.on_trade_tick,
        )
```

### 2. 数据处理回调

```python
def on_quote_tick(self, tick: QuoteTick) -> None:
    """处理报价 tick"""
    self.last_ask = tick.ask_price
    self.last_bid = tick.bid_price
    
    # 计算价差
    spread = tick.ask_price - tick.bid_price
    
    # 更新指标
    self.spread_indicator.update(spread)
    
    # 检查交易信号
    if self.should_enter_position(tick):
        self.enter_position()

def on_bar(self, bar: Bar) -> None:
    """处理 K 线数据"""
    # 更新技术指标
    self.sma.update(bar.close)
    self.rsi.update(bar.close)
    
    # 策略逻辑
    if self.sma.value > bar.close and self.rsi.value < 30:
        self.buy_signal()
```

## 📈 订单管理

### 1. 订单创建

```python
def create_orders(self):
    """创建不同类型的订单"""
    
    # 市价单
    market_order = self.order_factory.market(
        instrument_id=self.instrument_id,
        order_side=OrderSide.BUY,
        quantity=Quantity.from_str("1.0"),
    )
    
    # 限价单
    limit_order = self.order_factory.limit(
        instrument_id=self.instrument_id,
        order_side=OrderSide.SELL,
        quantity=Quantity.from_str("1.0"),
        price=Price.from_str("50000.00"),
        time_in_force=TimeInForce.GTC,
    )
    
    # 止损单
    stop_order = self.order_factory.stop_market(
        instrument_id=self.instrument_id,
        order_side=OrderSide.SELL,
        quantity=Quantity.from_str("1.0"),
        trigger_price=Price.from_str("49000.00"),
    )
    
    # 追踪止损单
    trailing_stop = self.order_factory.trailing_stop_market(
        instrument_id=self.instrument_id,
        order_side=OrderSide.SELL,
        quantity=Quantity.from_str("1.0"),
        trailing_offset=Decimal("100.00"),
        trailing_offset_type=TrailingOffsetType.PRICE,
    )
```

### 2. 订单提交和管理

```python
def manage_orders(self):
    """订单提交和管理"""
    
    # 提交单个订单
    self.submit_order(self.create_market_order())
    
    # 批量提交订单（原子操作）
    order_list = OrderList([
        self.create_entry_order(),
        self.create_stop_loss_order(),
        self.create_take_profit_order(),
    ])
    self.submit_order_list(order_list)
    
    # 修改订单
    self.modify_order(
        order=existing_order,
        quantity=Quantity.from_str("2.0"),
        price=Price.from_str("51000.00"),
    )
    
    # 取消订单
    self.cancel_order(order)
    
    # 取消所有订单
    self.cancel_all_orders(self.instrument_id)
```

## 🎮 事件处理

### 1. 订单事件

```python
def on_order_initialized(self, event: OrderInitialized) -> None:
    """订单初始化"""
    self.log.info(f"Order initialized: {event.client_order_id}")

def on_order_submitted(self, event: OrderSubmitted) -> None:
    """订单已提交"""
    self.pending_orders.add(event.client_order_id)

def on_order_accepted(self, event: OrderAccepted) -> None:
    """订单被接受"""
    self.log.info(f"Order accepted by venue: {event.client_order_id}")

def on_order_rejected(self, event: OrderRejected) -> None:
    """订单被拒绝"""
    self.log.warning(f"Order rejected: {event.reason}")
    self.handle_rejection(event)

def on_order_filled(self, event: OrderFilled) -> None:
    """订单成交"""
    self.log.info(
        f"Order filled: {event.last_qty} @ {event.last_px}"
    )
    
    # 更新策略状态
    self.update_position_tracking(event)
    
    # 可能触发后续动作
    if event.order_side == OrderSide.BUY:
        self.place_take_profit_order(event)
```

### 2. 仓位事件

```python
def on_position_opened(self, event: PositionOpened) -> None:
    """仓位开启"""
    self.log.info(f"Position opened: {event.position_id}")
    
    # 设置止损
    self.set_position_stop_loss(event.position)
    
def on_position_changed(self, event: PositionChanged) -> None:
    """仓位变化"""
    position = self.cache.position(event.position_id)
    
    # 动态调整止损
    if position.is_long:
        self.trail_stop_loss(position)
        
def on_position_closed(self, event: PositionClosed) -> None:
    """仓位关闭"""
    self.log.info(
        f"Position closed: PnL = {event.realized_pnl}"
    )
    
    # 记录交易结果
    self.record_trade_result(event)
```

## 💡 高级特性

### 1. 指标集成

```python
from nautilus_trader.indicators.average.sma import SimpleMovingAverage
from nautilus_trader.indicators.rsi import RelativeStrengthIndex

class IndicatorStrategy(Strategy):
    
    def __init__(self, config):
        super().__init__(config)
        
        # 创建指标
        self.fast_sma = SimpleMovingAverage(10)
        self.slow_sma = SimpleMovingAverage(30)
        self.rsi = RelativeStrengthIndex(14)
        
    def on_bar(self, bar: Bar):
        # 更新指标
        self.fast_sma.update_raw(bar.close.as_double())
        self.slow_sma.update_raw(bar.close.as_double())
        self.rsi.update_raw(bar.close.as_double())
        
        # 交易信号
        if self.fast_sma.value > self.slow_sma.value:
            if self.rsi.value < 70:  # 非超买
                self.enter_long()
```

### 2. 风险管理

```python
class RiskManagedStrategy(Strategy):
    
    def calculate_position_size(self, stop_distance: float) -> Quantity:
        """基于风险计算仓位大小"""
        account = self.portfolio.account(self.account_id)
        
        # 每笔交易风险 1% 账户余额
        risk_amount = account.balance_total_USD * 0.01
        
        # 计算仓位大小
        position_size = risk_amount / stop_distance
        
        # 应用杠杆限制
        max_size = account.balance_total_USD * self.max_leverage
        position_size = min(position_size, max_size)
        
        return Quantity.from_int(int(position_size))
    
    def check_risk_limits(self) -> bool:
        """检查风险限制"""
        # 检查最大仓位数
        if len(self.cache.positions()) >= self.max_positions:
            return False
            
        # 检查日内亏损限制
        daily_pnl = self.portfolio.unrealized_pnl(USD)
        if daily_pnl < -self.max_daily_loss:
            self.log.warning("Daily loss limit reached")
            return False
            
        return True
```

### 3. 多时间框架

```python
class MultiTimeframeStrategy(Strategy):
    
    def on_start(self):
        # 订阅多个时间框架
        self.subscribe_bars(
            BarType.from_str("BTCUSDT.BINANCE-1-MINUTE-LAST"),
            handler=self.on_1min_bar,
        )
        self.subscribe_bars(
            BarType.from_str("BTCUSDT.BINANCE-15-MINUTE-LAST"),
            handler=self.on_15min_bar,
        )
        self.subscribe_bars(
            BarType.from_str("BTCUSDT.BINANCE-1-HOUR-LAST"),
            handler=self.on_1hour_bar,
        )
    
    def on_1hour_bar(self, bar: Bar):
        """确定总体趋势"""
        self.trend = self.determine_trend(bar)
    
    def on_15min_bar(self, bar: Bar):
        """寻找入场点"""
        if self.trend == "UP":
            self.find_long_entry(bar)
    
    def on_1min_bar(self, bar: Bar):
        """精确入场时机"""
        if self.pending_entry:
            self.execute_entry(bar)
```

### 4. 组合策略

```python
class PortfolioStrategy(Strategy):
    """管理多个交易对的策略"""
    
    def __init__(self, config):
        super().__init__(config)
        self.instruments = [
            "BTCUSDT.BINANCE",
            "ETHUSDT.BINANCE", 
            "BNBUSDT.BINANCE",
        ]
        self.correlations = {}
        
    def on_start(self):
        # 为每个交易对订阅数据
        for instrument in self.instruments:
            self.subscribe_quote_ticks(
                InstrumentId.from_str(instrument),
                handler=self.on_quote_tick,
            )
    
    def calculate_portfolio_exposure(self) -> dict:
        """计算组合暴露"""
        exposure = {}
        for position in self.cache.positions():
            symbol = position.instrument_id.symbol.value
            exposure[symbol] = position.quantity
        return exposure
    
    def rebalance_portfolio(self):
        """重新平衡组合"""
        target_weights = self.calculate_target_weights()
        current_exposure = self.calculate_portfolio_exposure()
        
        for instrument, target_weight in target_weights.items():
            current_weight = current_exposure.get(instrument, 0)
            diff = target_weight - current_weight
            
            if abs(diff) > self.rebalance_threshold:
                self.adjust_position(instrument, diff)
```

## 🛠️ 实用工具

### 1. 定时器

```python
def on_start(self):
    # 设置定时器
    self.clock.set_timer(
        name="check_positions",
        interval=timedelta(minutes=5),
        callback=self.check_positions,
    )
    
def check_positions(self, event: TimeEvent):
    """定期检查仓位"""
    for position in self.cache.positions():
        if position.unrealized_pnl < -self.max_loss:
            self.close_position(position)
```

### 2. 消息总线

```python
def on_start(self):
    # 订阅自定义消息
    self.msgbus.subscribe(
        topic="RISK.ALERT",
        handler=self.on_risk_alert,
    )
    
def publish_signal(self, signal: dict):
    """发布交易信号"""
    self.msgbus.publish(
        topic="STRATEGY.SIGNAL",
        msg=TradingSignal(**signal),
    )
```

### 3. 缓存访问

```python
def analyze_market_state(self):
    """分析市场状态"""
    
    # 获取最新报价
    quote = self.cache.quote_tick(self.instrument_id)
    
    # 获取订单簿
    book = self.cache.order_book(self.instrument_id)
    
    # 获取最近的K线
    bars = self.cache.bars(
        bar_type=self.bar_type,
        limit=100,
    )
    
    # 获取账户信息
    account = self.cache.account(self.account_id)
    
    # 获取所有未平仓位
    positions = self.cache.positions_open(
        instrument_id=self.instrument_id,
    )
```

## 📊 性能优化

### 1. 批量操作

```python
def process_batch_signals(self, signals: list):
    """批量处理信号"""
    orders = []
    
    for signal in signals:
        if signal.action == "BUY":
            order = self.create_buy_order(signal)
            orders.append(order)
    
    # 批量提交
    if orders:
        order_list = OrderList(orders)
        self.submit_order_list(order_list)
```

### 2. 缓存优化

```python
class CachedStrategy(Strategy):
    
    def __init__(self, config):
        super().__init__(config)
        self._price_cache = {}
        self._indicator_cache = {}
        
    def on_quote_tick(self, tick: QuoteTick):
        # 缓存价格
        self._price_cache[tick.instrument_id] = tick
        
        # 定期清理
        if len(self._price_cache) > 1000:
            self._cleanup_cache()
```

## 🔒 安全实践

### 1. 参数验证

```python
def submit_order_safe(self, quantity: float, price: float):
    """安全的订单提交"""
    
    # 验证参数
    if quantity <= 0:
        raise ValueError("Quantity must be positive")
        
    if price <= 0:
        raise ValueError("Price must be positive")
        
    # 检查账户余额
    if not self.has_sufficient_balance(quantity, price):
        self.log.warning("Insufficient balance")
        return
        
    # 提交订单
    order = self.create_order(quantity, price)
    self.submit_order(order)
```

### 2. 异常处理

```python
def on_data(self, data):
    """安全的数据处理"""
    try:
        self.process_data(data)
    except ValueError as e:
        self.log.error(f"Data processing error: {e}")
        self.recover_from_error()
    except Exception as e:
        self.log.critical(f"Unexpected error: {e}")
        self.emergency_stop()
```

## 🚀 最佳实践

1. **策略设计**
   - 保持策略逻辑简洁清晰
   - 分离关注点（数据、信号、执行）
   - 使用配置文件管理参数

2. **性能优化**
   - 避免在热路径上的重复计算
   - 使用批量操作减少开销
   - 合理使用缓存

3. **风险管理**
   - 始终设置止损
   - 实施仓位大小管理
   - 监控关键风险指标

4. **测试实践**
   - 充分回测策略
   - 使用模拟账户验证
   - 逐步扩大真实交易规模

---

*本文档详细分析了 Nautilus Trader 的 Python 策略框架设计*
