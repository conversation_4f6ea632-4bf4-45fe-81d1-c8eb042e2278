# Phoenix TT Quant - NautilusTrader 重构项目

使用 Phoenix + Rust（Rustler）重构 NautilusTrader，构建高性能、高可用的量化交易系统。

## 项目概述

本项目旨在将 NautilusTrader 的核心功能迁移到 Phoenix + Rust 混合架构：

- **Phoenix/Elixir**：负责控制面、编排、可观测性、人机交互
- **Rust（通过 Rustler）**：负责数据面、计算热路径、性能关键组件

### 架构优势

- **低延迟 + 高容错**：Rust 保证性能，BEAM 提供容错
- **可扩展性**：OTP 天然支持分布式与并发
- **可观测性**：LiveView 实时监控，Telemetry 全链路追踪
- **开发效率**：Phoenix 生态成熟，开发体验优秀

## 文档结构

### 📁 analysis/ - 项目分析

了解 NautilusTrader 现状，为重构提供基础

- `01_nautilus_rust_core.md` - Rust 核心代码深度分析
- `02_nautilus_python_strategy.md` - Python 策略框架分析
- `03_rust_architecture_patterns.md` - Rust 架构模式研究
- `04_python_rust_integration.md` - Python-Rust 集成分析
- `05_system_integration_deployment.md` - 系统集成部署分析

### 📋 planning/ - 重构计划

设计新架构的方案与规划

- `01_phoenix_rust_responsibilities.md` - Phoenix 与 Rust 职责划分
- `02_interface_design.md` - 接口原型与示例代码
- `03_data_contracts.md` - 数据契约规范
- `04_migration_roadmap.md` - 迁移路线图

### 🔧 implementation/ - 技术实现

具体的实现方案与代码示例

- `01_project_structure.md` - Phoenix umbrella 项目结构
- `02_minimal_poc.md` - 最小可用 POC 示例
- `03_performance_benchmarks.md` - 性能基准测试框架
- `04_rust_learning_guide.md` - Rust 学习指南

### 📦 examples/ - 示例代码

可运行的代码示例和 POC 项目

## 快速开始

### 1. 了解现状

从 `analysis/` 目录开始，了解 NautilusTrader 的架构和设计

### 2. 查看计划

阅读 `planning/` 目录中的重构方案和职责划分

### 3. 动手实践

参考 `implementation/` 目录中的具体实现方案

### 4. 运行示例

尝试 `examples/` 目录中的 POC 项目

## 核心设计理念

### 🎯 职责分离

- **Phoenix/Elixir**: 控制面、编排、可观测性
- **Rust (Rustler)**: 数据面、计算热路径

### ⚡ 性能目标

- 订单簿更新: < 20µs
- 市场数据解码: < 50µs
- 风控检查: < 30µs
- BEAM 边界开销: < 10µs

### 🛡️ 容错设计

- NIF 错误隔离
- 进程监督树
- 优雅降级
- 快速恢复

## 贡献指南

1. 选择感兴趣的模块
2. 阅读相关文档
3. 提出改进建议
4. 提交 Pull Request

## 许可证

本项目采用 LGPL-3.0 许可证，与 NautilusTrader 保持一致。
